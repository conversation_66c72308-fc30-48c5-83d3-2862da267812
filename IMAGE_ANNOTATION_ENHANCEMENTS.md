# 🎨 图像标注功能增强

## 📋 新增功能概述

在原有图像标注功能基础上，新增了三个重要功能：

1. **图片列表显示** - 右侧面板显示数据集中的所有图片
2. **实时鼠标坐标跟踪** - 在canvas中实时显示鼠标位置坐标
3. **可编辑标签名称** - 双击标签可以直接在canvas中编辑

## 🔄 完整功能流程

### 1. 访问标注页面
- URL: `/annotations/image-annotate?datasetId=1`
- 自动加载指定数据集的所有图片
- 显示图片列表和标注工具

### 2. 图片列表功能
- **位置**: 页面右侧面板
- **显示内容**: 
  - 图片缩略图 (12x12像素)
  - 图片名称
  - 图片尺寸 (宽×高)
  - 图片序号
- **交互功能**:
  - 点击切换到对应图片
  - 当前图片高亮显示
  - 切换图片时自动清空标注

### 3. 实时鼠标坐标
- **显示位置**: 跟随鼠标移动
- **坐标格式**: `(x, y)` 像素坐标
- **智能定位**: 
  - 自动避免超出canvas边界
  - 鼠标右侧/下方优先显示
  - 边界时自动调整到左侧/上方
- **样式**: 黑色半透明背景，白色文字

### 4. 可编辑标签名称
- **触发方式**: 双击标签文字
- **编辑界面**: 浮动输入框
- **操作方式**:
  - Enter键确认修改
  - Escape键取消修改
  - 失去焦点自动保存
- **视觉反馈**: 选中标注时标签有蓝色背景

## 🛠 技术实现

### 页面布局调整

#### 原布局 (5列)
```
[工具栏] [标注画布(3列)] [标注列表]
```

#### 新布局 (6列)
```
[工具栏] [标注画布(3列)] [图片列表(2列)]
```

### 图片列表组件

```typescript
// 图片列表渲染
{imageList.map((image, index) => (
  <div
    key={image.id}
    className={`flex items-center space-x-3 p-2 rounded-lg border cursor-pointer ${
      currentImageIndex === index ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
    }`}
    onClick={() => {
      setCurrentImageIndex(index)
      setSelectedImage(image)
      setAnnotations([]) // 清空标注
    }}
  >
    <img src={image.url} className="w-12 h-12 object-cover rounded" />
    <div className="flex-1">
      <div className="text-sm font-medium">{image.name}</div>
      <div className="text-xs text-gray-500">{image.width} × {image.height}</div>
    </div>
    <span className="text-xs text-gray-400">{index + 1}</span>
  </div>
))}
```

### 鼠标坐标跟踪

```typescript
// 状态管理
const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null)

// 鼠标移动事件
const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
  const coords = getCanvasCoordinates(e)
  setMousePosition(coords) // 更新鼠标位置
  // ... 其他逻辑
}

// Canvas绘制坐标
if (mousePosition) {
  const coordText = `(${Math.round(mousePosition.x)}, ${Math.round(mousePosition.y)})`
  // 智能定位逻辑
  // 绘制背景和文字
}
```

### 可编辑标签功能

```typescript
// 状态管理
const [editingLabel, setEditingLabel] = useState<string | null>(null)
const [editingLabelValue, setEditingLabelValue] = useState('')

// 双击事件处理
const handleDoubleClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
  const coords = getCanvasCoordinates(e)
  const clickedAnnotation = annotations.find(annotation => {
    // 检测是否点击在标签文字区域
    const labelX = annotation.coordinates[0]
    const labelY = annotation.coordinates[1] - 8
    // 计算文字宽度和区域
    return coords.x >= labelX && coords.x <= labelX + textWidth &&
           coords.y >= labelY - 16 && coords.y <= labelY + 4
  })
  
  if (clickedAnnotation) {
    setEditingLabel(clickedAnnotation.id)
    setEditingLabelValue(clickedAnnotation.label)
  }
}

// 浮动编辑输入框
{editingLabel && (
  <input
    value={editingLabelValue}
    onChange={(e) => setEditingLabelValue(e.target.value)}
    onBlur={() => {
      onAnnotationUpdate(editingLabel, { label: editingLabelValue.trim() })
      setEditingLabel(null)
    }}
    onKeyDown={(e) => {
      if (e.key === 'Enter') {
        onAnnotationUpdate(editingLabel, { label: editingLabelValue.trim() })
        setEditingLabel(null)
      }
    }}
    style={{
      position: 'absolute',
      left: annotation.coordinates[0],
      top: annotation.coordinates[1] - 30
    }}
    autoFocus
  />
)}
```

## 🎨 用户界面改进

### 图片列表面板
- **标题**: "图片列表 (X 张)"
- **滚动**: 最大高度96，超出时显示滚动条
- **状态指示**: 当前图片蓝色高亮
- **空状态**: 显示图标和提示文字

### 坐标显示
- **背景**: `rgba(0, 0, 0, 0.8)` 半透明黑色
- **文字**: 白色，12px Arial字体
- **边距**: 背景内边距5px，文字外边距10px
- **定位**: 智能避让边界

### 标签编辑
- **选中状态**: 蓝色半透明背景 + 蓝色边框
- **编辑框**: 白色背景，蓝色边框，阴影效果
- **位置**: 标签上方30px处

## 📊 数据流

### 图片切换流程
1. 用户点击图片列表中的图片
2. 更新 `currentImageIndex` 和 `selectedImage`
3. 清空当前标注 `setAnnotations([])`
4. 重新渲染canvas和标注工具

### 坐标跟踪流程
1. 鼠标在canvas上移动
2. `handleMouseMove` 计算相对坐标
3. 更新 `mousePosition` 状态
4. `drawAnnotations` 函数绘制坐标显示
5. 鼠标离开时清空坐标

### 标签编辑流程
1. 双击标签文字区域
2. 检测点击位置是否在标签范围内
3. 设置 `editingLabel` 和 `editingLabelValue`
4. 显示浮动输入框
5. 用户编辑后保存或取消
6. 调用 `onAnnotationUpdate` 更新标注

## ✅ 测试要点

### 功能测试
- [ ] 图片列表正确显示所有图片
- [ ] 点击图片能正确切换
- [ ] 鼠标坐标实时跟随显示
- [ ] 双击标签能进入编辑模式
- [ ] 标签编辑保存和取消功能正常

### 交互测试
- [ ] 图片切换时标注被清空
- [ ] 坐标显示不会超出canvas边界
- [ ] 标签编辑框位置正确
- [ ] 键盘快捷键(Enter/Escape)正常工作

### 视觉测试
- [ ] 当前图片在列表中高亮显示
- [ ] 坐标显示清晰可读
- [ ] 选中标注的视觉反馈明显
- [ ] 编辑框样式美观

## 🚀 使用说明

1. **查看图片列表**: 右侧面板显示所有图片，点击切换
2. **查看鼠标坐标**: 在canvas上移动鼠标即可看到实时坐标
3. **编辑标签**: 双击任意标注的标签文字即可编辑
4. **保存编辑**: 按Enter键或点击其他地方保存，按Escape取消

这些增强功能大大提升了图像标注的用户体验和工作效率！

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 356,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 351,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"YTGJpgkBX9L8LqWTy4YRdKpxjIg=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        const coords = getCanvasCoordinates(e);\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseUp\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"IF/NHC1S/4IdN6zLjxgMVA5nX/0=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
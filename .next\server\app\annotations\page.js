/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/annotations/page";
exports.ids = ["app/annotations/page"];
exports.modules = {

/***/ "(rsc)/./app/annotations/page.tsx":
/*!**********************************!*\
  !*** ./app/annotations/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\app\\annotations\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./styles/globals.css\");\n/* harmony import */ var _components_layout_layout_wrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/layout/layout-wrapper */ \"(rsc)/./components/layout/layout-wrapper.tsx\");\n\n\n\n\nconst metadata = {\n    title: '深眸 - AI 智能数据标注平台',\n    description: '一个支持多模态数据、高效协作、可扩展的 AI 数据标注平台，集成模型预标注、任务管理、角色权限控制与可视化标注工具。'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_layout_wrapper__WEBPACK_IMPORTED_MODULE_2__.LayoutWrapper, {\n                children: children\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFLTUE7QUFIdUI7QUFDc0M7QUFJNUQsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7c0JBQzlCLDRFQUFDQyw0RUFBYUE7MEJBQUVLOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXhCIiwic291cmNlcyI6WyJGOlxcZGVlcHNpZ2h0XFxmcm9uZXRlbmRcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBMYXlvdXRXcmFwcGVyIH0gZnJvbSAnLi4vY29tcG9uZW50cy9sYXlvdXQvbGF5b3V0LXdyYXBwZXInXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfmt7HnnLggLSBBSSDmmbrog73mlbDmja7moIfms6jlubPlj7AnLFxuICBkZXNjcmlwdGlvbjogJ+S4gOS4quaUr+aMgeWkmuaooeaAgeaVsOaNruOAgemrmOaViOWNj+S9nOOAgeWPr+aJqeWxleeahCBBSSDmlbDmja7moIfms6jlubPlj7DvvIzpm4bmiJDmqKHlnovpooTmoIfms6jjgIHku7vliqHnrqHnkIbjgIHop5LoibLmnYPpmZDmjqfliLbkuI7lj6/op4bljJbmoIfms6jlt6XlhbfjgIInLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiemgtQ05cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPExheW91dFdyYXBwZXI+e2NoaWxkcmVufTwvTGF5b3V0V3JhcHBlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkxheW91dFdyYXBwZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/routes */ \"(rsc)/./constants/routes.ts\");\n\n\n\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"mt-4 text-6xl font-bold text-gray-900\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-2 text-2xl font-semibold text-gray-700\",\n                            children: \"页面未找到\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"很抱歉，您访问的页面不存在\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"可能的原因\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"请检查以下几点\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 网址输入错误\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 页面已被移动或删除\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 您没有访问权限\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 链接已过期\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.history.back(),\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"返回上页\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: _constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.DASHBOARD,\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                                        lineNumber: 40,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"返回首页\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/layout-wrapper.tsx":
/*!**********************************************!*\
  !*** ./components/layout/layout-wrapper.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LayoutWrapper: () => (/* binding */ LayoutWrapper)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LayoutWrapper = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LayoutWrapper() from the server but LayoutWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\layout\\layout-wrapper.tsx",
"LayoutWrapper",
);

/***/ }),

/***/ "(rsc)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Button: () => (/* binding */ Button),
/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Button = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\button.tsx",
"Button",
);const buttonVariants = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call buttonVariants() from the server but buttonVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\button.tsx",
"buttonVariants",
);

/***/ }),

/***/ "(rsc)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Card: () => (/* binding */ Card),
/* harmony export */   CardContent: () => (/* binding */ CardContent),
/* harmony export */   CardDescription: () => (/* binding */ CardDescription),
/* harmony export */   CardFooter: () => (/* binding */ CardFooter),
/* harmony export */   CardHeader: () => (/* binding */ CardHeader),
/* harmony export */   CardTitle: () => (/* binding */ CardTitle)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Card = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"Card",
);const CardHeader = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardHeader",
);const CardFooter = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardFooter",
);const CardTitle = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardTitle",
);const CardDescription = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardDescription",
);const CardContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardContent",
);

/***/ }),

/***/ "(rsc)/./constants/routes.ts":
/*!*****************************!*\
  !*** ./constants/routes.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAV_ITEMS: () => (/* binding */ NAV_ITEMS),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES)\n/* harmony export */ });\nconst ROUTES = {\n    HOME: '/',\n    LOGIN: '/login',\n    DASHBOARD: '/dashboard',\n    // Project Management\n    PROJECTS: '/projects',\n    PROJECT_DETAIL: (id)=>`/projects/${id}`,\n    PROJECT_CREATE: '/projects/create',\n    PROJECT_EDIT: (id)=>`/projects/${id}/edit`,\n    // Dataset Management\n    DATASETS: '/datasets',\n    DATASET_DETAIL: (id)=>`/datasets/${id}`,\n    DATASET_CREATE: '/datasets/create',\n    DATASET_EDIT: (id)=>`/datasets/${id}/edit`,\n    // Model Management\n    MODELS: '/models',\n    MODEL_DETAIL: (id)=>`/models/${id}`,\n    MODEL_CREATE: '/models/create',\n    MODEL_EDIT: (id)=>`/models/${id}/edit`,\n    // Annotation Management\n    ANNOTATIONS: '/annotations',\n    ANNOTATION_DETAIL: (id)=>`/annotations/${id}`,\n    ANNOTATION_CREATE: '/annotations/create',\n    ANNOTATION_EDIT: (id)=>`/annotations/${id}/edit`,\n    IMAGE_ANNOTATE: '/annotations/image-annotate',\n    IMAGE_ANNOTATE_DATASET: (datasetId)=>`/annotations/image-annotate?datasetId=${datasetId}`,\n    // Task Management\n    TASKS: '/tasks',\n    TASK_DETAIL: (id)=>`/tasks/${id}`,\n    TASK_CREATE: '/tasks/create',\n    TASK_EDIT: (id)=>`/tasks/${id}/edit`,\n    // User Management\n    PROFILE: '/profile',\n    SETTINGS: '/settings'\n};\nconst NAV_ITEMS = [\n    {\n        title: '项目管理',\n        href: ROUTES.PROJECTS,\n        children: [\n            {\n                title: '项目列表',\n                href: ROUTES.PROJECTS\n            },\n            {\n                title: '项目创建',\n                href: ROUTES.PROJECT_CREATE\n            }\n        ]\n    },\n    {\n        title: '数据集管理',\n        href: ROUTES.DATASETS,\n        children: [\n            {\n                title: '数据集列表',\n                href: ROUTES.DATASETS\n            },\n            {\n                title: '数据集创建',\n                href: ROUTES.DATASET_CREATE\n            }\n        ]\n    },\n    {\n        title: '模型管理',\n        href: ROUTES.MODELS,\n        children: [\n            {\n                title: '模型列表',\n                href: ROUTES.MODELS\n            },\n            {\n                title: '模型创建',\n                href: ROUTES.MODEL_CREATE\n            }\n        ]\n    },\n    {\n        title: '标注管理',\n        href: ROUTES.ANNOTATIONS,\n        children: [\n            {\n                title: '标注列表',\n                href: ROUTES.ANNOTATIONS\n            },\n            {\n                title: '标注创建',\n                href: ROUTES.ANNOTATION_CREATE\n            }\n        ]\n    },\n    {\n        title: '任务管理',\n        href: ROUTES.TASKS,\n        children: [\n            {\n                title: '任务列表',\n                href: ROUTES.TASKS\n            },\n            {\n                title: '任务创建',\n                href: ROUTES.TASK_CREATE\n            }\n        ]\n    },\n    {\n        title: '个人中心',\n        href: ROUTES.PROFILE\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./constants/routes.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/annotations/page.tsx */ \"(rsc)/./app/annotations/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'annotations',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\layout.tsx\"],\n'error': [module1, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\"],\n'not-found': [module2, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/annotations/page\",\n        pathname: \"/annotations\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/annotations/page.tsx */ \"(rsc)/./app/annotations/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNhbm5vdGF0aW9ucyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBeUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXGRlZXBzaWdodFxcXFxmcm9uZXRlbmRcXFxcYXBwXFxcXGFubm90YXRpb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBJQUE2RSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Clayout%5C%5Clayout-wrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Clayout%5C%5Clayout-wrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/layout-wrapper.tsx */ \"(rsc)/./components/layout/layout-wrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0JTVDJTVDbGF5b3V0LXdyYXBwZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTGF5b3V0V3JhcHBlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDZGVlcHNpZ2h0JTVDJTVDZnJvbmV0ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNzdHlsZXMlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJMYXlvdXRXcmFwcGVyXCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxsYXlvdXQtd3JhcHBlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Clayout%5C%5Clayout-wrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/button.tsx */ \"(rsc)/./components/ui/button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/card.tsx */ \"(rsc)/./components/ui/card.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNidXR0b24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNjYXJkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNhcmQlMjIlMkMlMjJDYXJkSGVhZGVyJTIyJTJDJTIyQ2FyZFRpdGxlJTIyJTJDJTIyQ2FyZERlc2NyaXB0aW9uJTIyJTJDJTIyQ2FyZENvbnRlbnQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRiUzQSU1QyU1Q2RlZXBzaWdodCU1QyU1Q2Zyb25ldGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXFIO0FBQ3JIO0FBQ0EsNEpBQTBLO0FBQzFLO0FBQ0EsZ05BQTJKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJGOlxcXFxkZWVwc2lnaHRcXFxcZnJvbmV0ZW5kXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcYnV0dG9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ2FyZFwiLFwiQ2FyZEhlYWRlclwiLFwiQ2FyZFRpdGxlXCIsXCJDYXJkRGVzY3JpcHRpb25cIixcIkNhcmRDb250ZW50XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxjb21wb25lbnRzXFxcXHVpXFxcXGNhcmQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e36c26631245\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkY6XFxkZWVwc2lnaHRcXGZyb25ldGVuZFxcc3R5bGVzXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUzNmMyNjYzMTI0NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(ssr)/./app/annotations/page.tsx":
/*!**********************************!*\
  !*** ./app/annotations/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnnotationsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants/routes */ \"(ssr)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Database,FileText,Image,MoreHorizontal,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Database,FileText,Image,MoreHorizontal,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Database,FileText,Image,MoreHorizontal,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Database,FileText,Image,MoreHorizontal,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Database,FileText,Image,MoreHorizontal,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Database,FileText,Image,MoreHorizontal,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Database,FileText,Image,MoreHorizontal,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _services_mockData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/mockData */ \"(ssr)/./services/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst statusColors = {\n    UPLOADING: 'bg-blue-100 text-blue-800',\n    PROCESSING: 'bg-yellow-100 text-yellow-800',\n    READY: 'bg-green-100 text-green-800',\n    ERROR: 'bg-red-100 text-red-800'\n};\nconst statusLabels = {\n    UPLOADING: '上传中',\n    PROCESSING: '处理中',\n    READY: '就绪',\n    ERROR: '错误'\n};\nfunction AnnotationsPage() {\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filter datasets that are ready for annotation (IMAGE type and READY status)\n    const annotationReadyDatasets = _services_mockData__WEBPACK_IMPORTED_MODULE_7__.mockDatasets.filter((dataset)=>dataset.type === 'IMAGE' && dataset.status === 'READY');\n    const filteredDatasets = annotationReadyDatasets.filter((dataset)=>dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) || dataset.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"标注管理\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"选择数据集开始图像标注\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: _constants_routes__WEBPACK_IMPORTED_MODULE_6__.ROUTES.IMAGE_ANNOTATE,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"直接进入标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: _constants_routes__WEBPACK_IMPORTED_MODULE_6__.ROUTES.ANNOTATION_CREATE,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"创建标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex-1 max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            placeholder: \"搜索数据集...\",\n                            value: searchTerm,\n                            onChange: (e)=>setSearchTerm(e.target.value),\n                            className: \"pl-10\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: filteredDatasets.map((dataset)=>{\n                    const project = (0,_services_mockData__WEBPACK_IMPORTED_MODULE_7__.getProjectById)(dataset.projectId);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: dataset.name\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    className: \"mt-2\",\n                                                    children: dataset.description\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `px-2 py-1 text-xs rounded-full ${statusColors[dataset.status]}`,\n                                                    children: statusLabels[dataset.status]\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        dataset.size.toLocaleString(),\n                                                        \" 张图片\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"项目：\",\n                                                        project?.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"创建：\",\n                                                        dataset.createdAt.toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: _constants_routes__WEBPACK_IMPORTED_MODULE_6__.ROUTES.DATASET_DETAIL(dataset.id),\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full\",\n                                                        children: \"查看详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: _constants_routes__WEBPACK_IMPORTED_MODULE_6__.ROUTES.IMAGE_ANNOTATE_DATASET(dataset.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Database_FileText_Image_MoreHorizontal_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"开始标记\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, dataset.id, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            filteredDatasets.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"没有找到可用于标注的数据集\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm mt-2\",\n                        children: '只有状态为\"就绪\"的图像数据集可以进行标注'\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/annotations/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            // Log the error to an error reporting service\n            console.error(error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-red-500\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"mt-4 text-3xl font-bold text-gray-900\",\n                            children: \"出错了\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"很抱歉，页面遇到了一些问题\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"错误详情\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: error.message || '发生了未知错误'\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: reset,\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"重试\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>window.location.href = '/dashboard',\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"返回首页\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/layout-wrapper.tsx":
/*!**********************************************!*\
  !*** ./components/layout/layout-wrapper.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutWrapper: () => (/* binding */ LayoutWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./navbar */ \"(ssr)/./components/layout/navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ LayoutWrapper auto */ \n\n\nfunction LayoutWrapper({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Pages that should not show the navbar\n    const noNavbarPages = [\n        '/login'\n    ];\n    const shouldShowNavbar = !noNavbarPages.includes(pathname);\n    if (!shouldShowNavbar) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navbar__WEBPACK_IMPORTED_MODULE_2__.Navbar, {}, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\layout-wrapper.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\",\n                children: children\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\layout-wrapper.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\layout-wrapper.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xheW91dC9sYXlvdXQtd3JhcHBlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTZDO0FBQ1o7QUFNMUIsU0FBU0UsY0FBYyxFQUFFQyxRQUFRLEVBQXNCO0lBQzVELE1BQU1DLFdBQVdKLDREQUFXQTtJQUU1Qix3Q0FBd0M7SUFDeEMsTUFBTUssZ0JBQWdCO1FBQUM7S0FBUztJQUVoQyxNQUFNQyxtQkFBbUIsQ0FBQ0QsY0FBY0UsUUFBUSxDQUFDSDtJQUVqRCxJQUFJLENBQUNFLGtCQUFrQjtRQUNyQixxQkFBTztzQkFBR0g7O0lBQ1o7SUFFQSxxQkFDRSw4REFBQ0s7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNSLDJDQUFNQTs7Ozs7MEJBQ1AsOERBQUNTO2dCQUFLRCxXQUFVOzBCQUNiTjs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkY6XFxkZWVwc2lnaHRcXGZyb25ldGVuZFxcY29tcG9uZW50c1xcbGF5b3V0XFxsYXlvdXQtd3JhcHBlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgTmF2YmFyIH0gZnJvbSAnLi9uYXZiYXInXG5cbmludGVyZmFjZSBMYXlvdXRXcmFwcGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMYXlvdXRXcmFwcGVyKHsgY2hpbGRyZW4gfTogTGF5b3V0V3JhcHBlclByb3BzKSB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuICBcbiAgLy8gUGFnZXMgdGhhdCBzaG91bGQgbm90IHNob3cgdGhlIG5hdmJhclxuICBjb25zdCBub05hdmJhclBhZ2VzID0gWycvbG9naW4nXVxuICBcbiAgY29uc3Qgc2hvdWxkU2hvd05hdmJhciA9ICFub05hdmJhclBhZ2VzLmluY2x1ZGVzKHBhdGhuYW1lKVxuXG4gIGlmICghc2hvdWxkU2hvd05hdmJhcikge1xuICAgIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgPE5hdmJhciAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHktNiBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L21haW4+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VQYXRobmFtZSIsIk5hdmJhciIsIkxheW91dFdyYXBwZXIiLCJjaGlsZHJlbiIsInBhdGhuYW1lIiwibm9OYXZiYXJQYWdlcyIsInNob3VsZFNob3dOYXZiYXIiLCJpbmNsdWRlcyIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/layout-wrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/navbar.tsx":
/*!**************************************!*\
  !*** ./components/layout/navbar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/routes */ \"(ssr)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _services_mockData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/mockData */ \"(ssr)/./services/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\n\n\nfunction Navbar() {\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            // Get current user from localStorage\n            const username = localStorage.getItem('currentUser') || 'admin';\n            setCurrentUser(username);\n        }\n    }[\"Navbar.useEffect\"], []);\n    const handleLogout = ()=>{\n        // Clear authentication data\n        localStorage.removeItem('currentUser');\n        localStorage.removeItem('isAuthenticated');\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.LOGIN);\n    };\n    // Get user display name\n    const getUserDisplayName = ()=>{\n        const user = _services_mockData__WEBPACK_IMPORTED_MODULE_5__.mockUsers.find((u)=>u.username === currentUser);\n        return user ? user.username : currentUser;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: _constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.DASHBOARD,\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: \"深眸\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: _constants_routes__WEBPACK_IMPORTED_MODULE_4__.NAV_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    onMouseEnter: ()=>setActiveDropdown(item.title),\n                                    onMouseLeave: ()=>setActiveDropdown(null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium\",\n                                            children: [\n                                                item.title,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"ml-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeDropdown === item.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg border z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-1\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        children: child.title\n                                                    }, child.title, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium\",\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.title, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center text-gray-700 hover:text-primary\",\n                                    onMouseEnter: ()=>setActiveDropdown('user'),\n                                    onMouseLeave: ()=>setActiveDropdown(null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"ml-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                activeDropdown === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-full right-0 mt-1 w-48 bg-white rounded-md shadow-lg border z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: _constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.PROFILE,\n                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"个人资料\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: _constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.SETTINGS,\n                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"设置\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleLogout,\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"退出登录\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\layout\\\\navbar.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xheW91dC9uYXZiYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ2Y7QUFDZTtBQUVXO0FBQ0U7QUFDVDtBQUV4QyxTQUFTVTtJQUNkLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR1osK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQ2EsYUFBYUMsZUFBZSxHQUFHZCwrQ0FBUUEsQ0FBUztJQUN2RCxNQUFNZSxTQUFTWiwwREFBU0E7SUFFeEJGLGdEQUFTQTs0QkFBQztZQUNSLHFDQUFxQztZQUNyQyxNQUFNZSxXQUFXQyxhQUFhQyxPQUFPLENBQUMsa0JBQWtCO1lBQ3hESixlQUFlRTtRQUNqQjsyQkFBRyxFQUFFO0lBRUwsTUFBTUcsZUFBZTtRQUNuQiw0QkFBNEI7UUFDNUJGLGFBQWFHLFVBQVUsQ0FBQztRQUN4QkgsYUFBYUcsVUFBVSxDQUFDO1FBRXhCTCxPQUFPTSxJQUFJLENBQUNoQixxREFBTUEsQ0FBQ2lCLEtBQUs7SUFDMUI7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTUMscUJBQXFCO1FBQ3pCLE1BQU1DLE9BQU9mLHlEQUFTQSxDQUFDZ0IsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFVixRQUFRLEtBQUtIO1FBQ2hELE9BQU9XLE9BQU9BLEtBQUtSLFFBQVEsR0FBR0g7SUFDaEM7SUFFQSxxQkFDRSw4REFBQ2M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7O2tDQUViLDhEQUFDQzt3QkFBSUQsV0FBVTtrQ0FDYiw0RUFBQzFCLGtEQUFJQTs0QkFBQzRCLE1BQU16QixxREFBTUEsQ0FBQzBCLFNBQVM7NEJBQUVILFdBQVU7c0NBQ3RDLDRFQUFDSTtnQ0FBR0osV0FBVTswQ0FBa0M7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3BELDhEQUFDQzt3QkFBSUQsV0FBVTtrQ0FDWnhCLHdEQUFTQSxDQUFDNkIsR0FBRyxDQUFDLENBQUNDLHFCQUNkLDhEQUFDTDtnQ0FBcUJELFdBQVU7MENBQzdCTSxLQUFLQyxRQUFRLGlCQUNaLDhEQUFDTjtvQ0FDQ0QsV0FBVTtvQ0FDVlEsY0FBYyxJQUFNeEIsa0JBQWtCc0IsS0FBS0csS0FBSztvQ0FDaERDLGNBQWMsSUFBTTFCLGtCQUFrQjs7c0RBRXRDLDhEQUFDMkI7NENBQU9YLFdBQVU7O2dEQUNmTSxLQUFLRyxLQUFLOzhEQUNYLDhEQUFDL0IsbUdBQVdBO29EQUFDc0IsV0FBVTs7Ozs7Ozs7Ozs7O3dDQUd4QmpCLG1CQUFtQnVCLEtBQUtHLEtBQUssa0JBQzVCLDhEQUFDUjs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ0M7Z0RBQUlELFdBQVU7MERBQ1pNLEtBQUtDLFFBQVEsQ0FBQ0YsR0FBRyxDQUFDLENBQUNPLHNCQUNsQiw4REFBQ3RDLGtEQUFJQTt3REFFSDRCLE1BQU1VLE1BQU1WLElBQUk7d0RBQ2hCRixXQUFVO2tFQUVUWSxNQUFNSCxLQUFLO3VEQUpQRyxNQUFNSCxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt5REFZNUIsOERBQUNuQyxrREFBSUE7b0NBQ0g0QixNQUFNSSxLQUFLSixJQUFJO29DQUNmRixXQUFVOzhDQUVUTSxLQUFLRyxLQUFLOzs7Ozs7K0JBakNQSCxLQUFLRyxLQUFLOzs7Ozs7Ozs7O2tDQXlDeEIsOERBQUNSO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNXO29DQUNDWCxXQUFVO29DQUNWUSxjQUFjLElBQU14QixrQkFBa0I7b0NBQ3RDMEIsY0FBYyxJQUFNMUIsa0JBQWtCOztzREFFdEMsOERBQUNMLG1HQUFJQTs0Q0FBQ3FCLFdBQVU7Ozs7OztzREFDaEIsOERBQUNhOzRDQUFLYixXQUFVO3NEQUF1Qkw7Ozs7OztzREFDdkMsOERBQUNqQixtR0FBV0E7NENBQUNzQixXQUFVOzs7Ozs7Ozs7Ozs7Z0NBR3hCakIsbUJBQW1CLHdCQUNsQiw4REFBQ2tCO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUMxQixrREFBSUE7Z0RBQ0g0QixNQUFNekIscURBQU1BLENBQUNxQyxPQUFPO2dEQUNwQmQsV0FBVTswREFDWDs7Ozs7OzBEQUdELDhEQUFDMUIsa0RBQUlBO2dEQUNINEIsTUFBTXpCLHFEQUFNQSxDQUFDc0MsUUFBUTtnREFDckJmLFdBQVU7MERBQ1g7Ozs7OzswREFHRCw4REFBQ1c7Z0RBQ0NLLFNBQVN6QjtnREFDVFMsV0FBVTs7a0VBRVYsOERBQUNwQixtR0FBTUE7d0RBQUNvQixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFZekQiLCJzb3VyY2VzIjpbIkY6XFxkZWVwc2lnaHRcXGZyb25ldGVuZFxcY29tcG9uZW50c1xcbGF5b3V0XFxuYXZiYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgTkFWX0lURU1TLCBST1VURVMgfSBmcm9tICdAL2NvbnN0YW50cy9yb3V0ZXMnXG5pbXBvcnQgeyBDaGV2cm9uRG93biwgVXNlciwgTG9nT3V0IH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgbW9ja1VzZXJzIH0gZnJvbSAnQC9zZXJ2aWNlcy9tb2NrRGF0YSdcblxuZXhwb3J0IGZ1bmN0aW9uIE5hdmJhcigpIHtcbiAgY29uc3QgW2FjdGl2ZURyb3Bkb3duLCBzZXRBY3RpdmVEcm9wZG93bl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbY3VycmVudFVzZXIsIHNldEN1cnJlbnRVc2VyXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBHZXQgY3VycmVudCB1c2VyIGZyb20gbG9jYWxTdG9yYWdlXG4gICAgY29uc3QgdXNlcm5hbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3VycmVudFVzZXInKSB8fCAnYWRtaW4nXG4gICAgc2V0Q3VycmVudFVzZXIodXNlcm5hbWUpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9ICgpID0+IHtcbiAgICAvLyBDbGVhciBhdXRoZW50aWNhdGlvbiBkYXRhXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2N1cnJlbnRVc2VyJylcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnaXNBdXRoZW50aWNhdGVkJylcblxuICAgIHJvdXRlci5wdXNoKFJPVVRFUy5MT0dJTilcbiAgfVxuXG4gIC8vIEdldCB1c2VyIGRpc3BsYXkgbmFtZVxuICBjb25zdCBnZXRVc2VyRGlzcGxheU5hbWUgPSAoKSA9PiB7XG4gICAgY29uc3QgdXNlciA9IG1vY2tVc2Vycy5maW5kKHUgPT4gdS51c2VybmFtZSA9PT0gY3VycmVudFVzZXIpXG4gICAgcmV0dXJuIHVzZXIgPyB1c2VyLnVzZXJuYW1lIDogY3VycmVudFVzZXJcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPG5hdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctc20gYm9yZGVyLWJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBoLTE2XCI+XG4gICAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPExpbmsgaHJlZj17Uk9VVEVTLkRBU0hCT0FSRH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnlcIj7mt7HnnLg8L2gxPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE5hdmlnYXRpb24gTWVudSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LThcIj5cbiAgICAgICAgICAgIHtOQVZfSVRFTVMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtpdGVtLnRpdGxlfSBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIHtpdGVtLmNoaWxkcmVuID8gKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiXG4gICAgICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KCkgPT4gc2V0QWN0aXZlRHJvcGRvd24oaXRlbS50aXRsZSl9XG4gICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0QWN0aXZlRHJvcGRvd24obnVsbCl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LXByaW1hcnkgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwibWwtMSBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICB7YWN0aXZlRHJvcGRvd24gPT09IGl0ZW0udGl0bGUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLWZ1bGwgbGVmdC0wIG10LTEgdy00OCBiZy13aGl0ZSByb3VuZGVkLW1kIHNoYWRvdy1sZyBib3JkZXIgei01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmNoaWxkcmVuLm1hcCgoY2hpbGQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtjaGlsZC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2NoaWxkLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2hpbGQudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LXByaW1hcnkgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXRlbS50aXRsZX1cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFVzZXIgSW5mbyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LXByaW1hcnlcIlxuICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KCkgPT4gc2V0QWN0aXZlRHJvcGRvd24oJ3VzZXInKX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eygpID0+IHNldEFjdGl2ZURyb3Bkb3duKG51bGwpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e2dldFVzZXJEaXNwbGF5TmFtZSgpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwibWwtMSBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7YWN0aXZlRHJvcGRvd24gPT09ICd1c2VyJyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCByaWdodC0wIG10LTEgdy00OCBiZy13aGl0ZSByb3VuZGVkLW1kIHNoYWRvdy1sZyBib3JkZXIgei01MFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgaHJlZj17Uk9VVEVTLlBST0ZJTEV9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICDkuKrkurrotYTmlplcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e1JPVVRFUy5TRVRUSU5HU31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIOiuvue9rlxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwIGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICDpgIDlh7rnmbvlvZVcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvbmF2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJMaW5rIiwidXNlUm91dGVyIiwiTkFWX0lURU1TIiwiUk9VVEVTIiwiQ2hldnJvbkRvd24iLCJVc2VyIiwiTG9nT3V0IiwibW9ja1VzZXJzIiwiTmF2YmFyIiwiYWN0aXZlRHJvcGRvd24iLCJzZXRBY3RpdmVEcm9wZG93biIsImN1cnJlbnRVc2VyIiwic2V0Q3VycmVudFVzZXIiLCJyb3V0ZXIiLCJ1c2VybmFtZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJoYW5kbGVMb2dvdXQiLCJyZW1vdmVJdGVtIiwicHVzaCIsIkxPR0lOIiwiZ2V0VXNlckRpc3BsYXlOYW1lIiwidXNlciIsImZpbmQiLCJ1IiwibmF2IiwiY2xhc3NOYW1lIiwiZGl2IiwiaHJlZiIsIkRBU0hCT0FSRCIsImgxIiwibWFwIiwiaXRlbSIsImNoaWxkcmVuIiwib25Nb3VzZUVudGVyIiwidGl0bGUiLCJvbk1vdXNlTGVhdmUiLCJidXR0b24iLCJjaGlsZCIsInNwYW4iLCJQUk9GSUxFIiwiU0VUVElOR1MiLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 48,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardFooter,CardTitle,CardDescription,CardContent auto */ \n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Input auto */ \n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJGOlxcZGVlcHNpZ2h0XFxmcm9uZXRlbmRcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./constants/routes.ts":
/*!*****************************!*\
  !*** ./constants/routes.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAV_ITEMS: () => (/* binding */ NAV_ITEMS),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES)\n/* harmony export */ });\nconst ROUTES = {\n    HOME: '/',\n    LOGIN: '/login',\n    DASHBOARD: '/dashboard',\n    // Project Management\n    PROJECTS: '/projects',\n    PROJECT_DETAIL: (id)=>`/projects/${id}`,\n    PROJECT_CREATE: '/projects/create',\n    PROJECT_EDIT: (id)=>`/projects/${id}/edit`,\n    // Dataset Management\n    DATASETS: '/datasets',\n    DATASET_DETAIL: (id)=>`/datasets/${id}`,\n    DATASET_CREATE: '/datasets/create',\n    DATASET_EDIT: (id)=>`/datasets/${id}/edit`,\n    // Model Management\n    MODELS: '/models',\n    MODEL_DETAIL: (id)=>`/models/${id}`,\n    MODEL_CREATE: '/models/create',\n    MODEL_EDIT: (id)=>`/models/${id}/edit`,\n    // Annotation Management\n    ANNOTATIONS: '/annotations',\n    ANNOTATION_DETAIL: (id)=>`/annotations/${id}`,\n    ANNOTATION_CREATE: '/annotations/create',\n    ANNOTATION_EDIT: (id)=>`/annotations/${id}/edit`,\n    IMAGE_ANNOTATE: '/annotations/image-annotate',\n    IMAGE_ANNOTATE_DATASET: (datasetId)=>`/annotations/image-annotate?datasetId=${datasetId}`,\n    // Task Management\n    TASKS: '/tasks',\n    TASK_DETAIL: (id)=>`/tasks/${id}`,\n    TASK_CREATE: '/tasks/create',\n    TASK_EDIT: (id)=>`/tasks/${id}/edit`,\n    // User Management\n    PROFILE: '/profile',\n    SETTINGS: '/settings'\n};\nconst NAV_ITEMS = [\n    {\n        title: '项目管理',\n        href: ROUTES.PROJECTS,\n        children: [\n            {\n                title: '项目列表',\n                href: ROUTES.PROJECTS\n            },\n            {\n                title: '项目创建',\n                href: ROUTES.PROJECT_CREATE\n            }\n        ]\n    },\n    {\n        title: '数据集管理',\n        href: ROUTES.DATASETS,\n        children: [\n            {\n                title: '数据集列表',\n                href: ROUTES.DATASETS\n            },\n            {\n                title: '数据集创建',\n                href: ROUTES.DATASET_CREATE\n            }\n        ]\n    },\n    {\n        title: '模型管理',\n        href: ROUTES.MODELS,\n        children: [\n            {\n                title: '模型列表',\n                href: ROUTES.MODELS\n            },\n            {\n                title: '模型创建',\n                href: ROUTES.MODEL_CREATE\n            }\n        ]\n    },\n    {\n        title: '标注管理',\n        href: ROUTES.ANNOTATIONS,\n        children: [\n            {\n                title: '标注列表',\n                href: ROUTES.ANNOTATIONS\n            },\n            {\n                title: '标注创建',\n                href: ROUTES.ANNOTATION_CREATE\n            }\n        ]\n    },\n    {\n        title: '任务管理',\n        href: ROUTES.TASKS,\n        children: [\n            {\n                title: '任务列表',\n                href: ROUTES.TASKS\n            },\n            {\n                title: '任务创建',\n                href: ROUTES.TASK_CREATE\n            }\n        ]\n    },\n    {\n        title: '个人中心',\n        href: ROUTES.PROFILE\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./constants/routes.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJGOlxcZGVlcHNpZ2h0XFxmcm9uZXRlbmRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/annotations/page.tsx */ \"(ssr)/./app/annotations/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNhbm5vdGF0aW9ucyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBeUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXGRlZXBzaWdodFxcXFxmcm9uZXRlbmRcXFxcYXBwXFxcXGFubm90YXRpb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(ssr)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBJQUE2RSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Clayout%5C%5Clayout-wrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Clayout%5C%5Clayout-wrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/layout-wrapper.tsx */ \"(ssr)/./components/layout/layout-wrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0JTVDJTVDbGF5b3V0LXdyYXBwZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTGF5b3V0V3JhcHBlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDZGVlcHNpZ2h0JTVDJTVDZnJvbmV0ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNzdHlsZXMlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJMYXlvdXRXcmFwcGVyXCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxsYXlvdXQtd3JhcHBlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Clayout%5C%5Clayout-wrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/button.tsx */ \"(ssr)/./components/ui/button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/card.tsx */ \"(ssr)/./components/ui/card.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNidXR0b24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNjYXJkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNhcmQlMjIlMkMlMjJDYXJkSGVhZGVyJTIyJTJDJTIyQ2FyZFRpdGxlJTIyJTJDJTIyQ2FyZERlc2NyaXB0aW9uJTIyJTJDJTIyQ2FyZENvbnRlbnQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRiUzQSU1QyU1Q2RlZXBzaWdodCU1QyU1Q2Zyb25ldGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXFIO0FBQ3JIO0FBQ0EsNEpBQTBLO0FBQzFLO0FBQ0EsZ05BQTJKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJGOlxcXFxkZWVwc2lnaHRcXFxcZnJvbmV0ZW5kXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcYnV0dG9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ2FyZFwiLFwiQ2FyZEhlYWRlclwiLFwiQ2FyZFRpdGxlXCIsXCJDYXJkRGVzY3JpcHRpb25cIixcIkNhcmRDb250ZW50XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxjb21wb25lbnRzXFxcXHVpXFxcXGNhcmQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./services/mockData.ts":
/*!******************************!*\
  !*** ./services/mockData.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllImages: () => (/* binding */ getAllImages),\n/* harmony export */   getAnnotationsByTaskId: () => (/* binding */ getAnnotationsByTaskId),\n/* harmony export */   getAnnotationsByUserId: () => (/* binding */ getAnnotationsByUserId),\n/* harmony export */   getDatasetById: () => (/* binding */ getDatasetById),\n/* harmony export */   getDatasetsByProjectId: () => (/* binding */ getDatasetsByProjectId),\n/* harmony export */   getImageById: () => (/* binding */ getImageById),\n/* harmony export */   getImagesByDatasetId: () => (/* binding */ getImagesByDatasetId),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProjectById: () => (/* binding */ getProjectById),\n/* harmony export */   getProjectsByUserId: () => (/* binding */ getProjectsByUserId),\n/* harmony export */   getTaskById: () => (/* binding */ getTaskById),\n/* harmony export */   getTasksByProjectId: () => (/* binding */ getTasksByProjectId),\n/* harmony export */   getTasksByUserId: () => (/* binding */ getTasksByUserId),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   mockAnnotations: () => (/* binding */ mockAnnotations),\n/* harmony export */   mockDatasets: () => (/* binding */ mockDatasets),\n/* harmony export */   mockImages: () => (/* binding */ mockImages),\n/* harmony export */   mockModels: () => (/* binding */ mockModels),\n/* harmony export */   mockProjects: () => (/* binding */ mockProjects),\n/* harmony export */   mockTasks: () => (/* binding */ mockTasks),\n/* harmony export */   mockUsers: () => (/* binding */ mockUsers)\n/* harmony export */ });\n// Mock Users\nconst mockUsers = [\n    {\n        id: '1',\n        username: 'admin',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'ADMIN',\n        createdAt: new Date('2023-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        username: 'zhangsan',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'ANNOTATOR',\n        createdAt: new Date('2023-02-01'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        username: 'lisi',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'MANAGER',\n        createdAt: new Date('2023-03-01'),\n        updatedAt: new Date('2024-01-15')\n    },\n    {\n        id: '4',\n        username: 'wangwu',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'ANNOTATOR',\n        createdAt: new Date('2023-04-01'),\n        updatedAt: new Date('2024-01-10')\n    }\n];\n// Mock Projects\nconst mockProjects = [\n    {\n        id: '1',\n        name: '图像分类项目',\n        description: '用于训练图像分类模型的数据标注项目，包含10个类别的图像数据',\n        status: 'ACTIVE',\n        createdBy: '1',\n        members: [\n            {\n                userId: '1',\n                role: 'OWNER',\n                joinedAt: new Date('2024-01-15')\n            },\n            {\n                userId: '2',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-16')\n            },\n            {\n                userId: '3',\n                role: 'ADMIN',\n                joinedAt: new Date('2024-01-17')\n            }\n        ],\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: '目标检测项目',\n        description: '自动驾驶场景下的目标检测数据标注项目',\n        status: 'ACTIVE',\n        createdBy: '1',\n        members: [\n            {\n                userId: '1',\n                role: 'OWNER',\n                joinedAt: new Date('2024-01-10')\n            },\n            {\n                userId: '2',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-11')\n            },\n            {\n                userId: '3',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-12')\n            },\n            {\n                userId: '4',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-13')\n            }\n        ],\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: '语音识别项目',\n        description: '多语言语音识别数据集标注项目',\n        status: 'COMPLETED',\n        createdBy: '3',\n        members: [\n            {\n                userId: '3',\n                role: 'OWNER',\n                joinedAt: new Date('2023-12-01')\n            },\n            {\n                userId: '4',\n                role: 'MEMBER',\n                joinedAt: new Date('2023-12-02')\n            }\n        ],\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: '文本分析项目',\n        description: '自然语言处理和情感分析项目',\n        status: 'PAUSED',\n        createdBy: '2',\n        members: [\n            {\n                userId: '2',\n                role: 'OWNER',\n                joinedAt: new Date('2024-01-05')\n            },\n            {\n                userId: '1',\n                role: 'ADMIN',\n                joinedAt: new Date('2024-01-06')\n            }\n        ],\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    }\n];\n// Mock Datasets\nconst mockDatasets = [\n    {\n        id: '1',\n        name: '图像分类数据集',\n        description: '包含10个类别的图像数据，用于训练分类模型',\n        type: 'IMAGE',\n        size: 10000,\n        status: 'READY',\n        projectId: '1',\n        createdBy: '1',\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: '目标检测数据集',\n        description: '自动驾驶场景下的目标检测数据',\n        type: 'IMAGE',\n        size: 5000,\n        status: 'PROCESSING',\n        projectId: '2',\n        createdBy: '1',\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: '语音识别数据集',\n        description: '多语言语音数据集，包含中英文语音',\n        type: 'AUDIO',\n        size: 2000,\n        status: 'READY',\n        projectId: '3',\n        createdBy: '3',\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: '文本情感数据集',\n        description: '用于情感分析的文本数据集',\n        type: 'TEXT',\n        size: 15000,\n        status: 'READY',\n        projectId: '4',\n        createdBy: '2',\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    },\n    {\n        id: '5',\n        name: '视频动作识别数据集',\n        description: '人体动作识别视频数据集',\n        type: 'VIDEO',\n        size: 800,\n        status: 'UPLOADING',\n        projectId: '1',\n        createdBy: '1',\n        createdAt: new Date('2024-01-18'),\n        updatedAt: new Date('2024-01-19')\n    }\n];\n// Mock Tasks\nconst mockTasks = [\n    {\n        id: '1',\n        name: '图像分类标注',\n        description: '对图像数据进行分类标注，包含10个类别',\n        type: 'CLASSIFICATION',\n        status: 'IN_PROGRESS',\n        projectId: '1',\n        datasetId: '1',\n        assignedTo: [\n            '2',\n            '3'\n        ],\n        createdBy: '1',\n        dueDate: new Date('2024-01-25'),\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: '目标检测标注',\n        description: '对自动驾驶场景进行目标检测标注',\n        type: 'DETECTION',\n        status: 'PENDING',\n        projectId: '2',\n        datasetId: '2',\n        assignedTo: [\n            '4'\n        ],\n        createdBy: '1',\n        dueDate: new Date('2024-01-30'),\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: '语音转录任务',\n        description: '将语音文件转录为文本',\n        type: 'TRANSCRIPTION',\n        status: 'COMPLETED',\n        projectId: '3',\n        datasetId: '3',\n        assignedTo: [\n            '4'\n        ],\n        createdBy: '3',\n        dueDate: new Date('2024-01-20'),\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: '情感分析标注',\n        description: '对文本进行情感极性标注',\n        type: 'CLASSIFICATION',\n        status: 'REVIEW',\n        projectId: '4',\n        datasetId: '4',\n        assignedTo: [\n            '2'\n        ],\n        createdBy: '2',\n        dueDate: new Date('2024-01-28'),\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    }\n];\nconst mockImages = [\n    {\n        id: 'img_001',\n        name: 'cat_sample.jpg',\n        url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 245760,\n        format: 'jpg',\n        datasetId: '1',\n        uploadedAt: new Date('2024-01-15'),\n        tags: [\n            '动物',\n            '猫',\n            '宠物'\n        ]\n    },\n    {\n        id: 'img_002',\n        name: 'street_scene.jpg',\n        url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 312450,\n        format: 'jpg',\n        datasetId: '2',\n        uploadedAt: new Date('2024-01-16'),\n        tags: [\n            '街道',\n            '城市',\n            '交通'\n        ]\n    },\n    {\n        id: 'img_003',\n        name: 'dog_portrait.jpg',\n        url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 198340,\n        format: 'jpg',\n        datasetId: '1',\n        uploadedAt: new Date('2024-01-17'),\n        tags: [\n            '动物',\n            '狗',\n            '宠物'\n        ]\n    },\n    {\n        id: 'img_004',\n        name: 'building_facade.jpg',\n        url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 287650,\n        format: 'jpg',\n        datasetId: '1',\n        uploadedAt: new Date('2024-01-18'),\n        tags: [\n            '建筑',\n            '城市',\n            '现代'\n        ]\n    },\n    {\n        id: 'img_005',\n        name: 'nature_landscape.jpg',\n        url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 356780,\n        format: 'jpg',\n        datasetId: '3',\n        uploadedAt: new Date('2024-01-19'),\n        tags: [\n            '自然',\n            '风景',\n            '山脉'\n        ]\n    },\n    // Additional images for dataset 1 (图像分类数据集)\n    {\n        id: 'img_006',\n        name: 'cat_portrait.jpg',\n        url: 'https://images.unsplash.com/photo-1574158622682-e40e69881006?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 280000,\n        format: 'jpg',\n        datasetId: '1',\n        uploadedAt: new Date('2024-01-16'),\n        tags: [\n            '动物',\n            '猫',\n            '肖像'\n        ]\n    },\n    {\n        id: 'img_007',\n        name: 'dog_playing.jpg',\n        url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 320000,\n        format: 'jpg',\n        datasetId: '1',\n        uploadedAt: new Date('2024-01-17'),\n        tags: [\n            '动物',\n            '狗',\n            '玩耍'\n        ]\n    },\n    {\n        id: 'img_008',\n        name: 'bird_flying.jpg',\n        url: 'https://images.unsplash.com/photo-1444464666168-49d633b86797?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 290000,\n        format: 'jpg',\n        datasetId: '1',\n        uploadedAt: new Date('2024-01-18'),\n        tags: [\n            '动物',\n            '鸟',\n            '飞行'\n        ]\n    },\n    // Additional images for dataset 2 (目标检测数据集)\n    {\n        id: 'img_009',\n        name: 'traffic_intersection.jpg',\n        url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 340000,\n        format: 'jpg',\n        datasetId: '2',\n        uploadedAt: new Date('2024-01-17'),\n        tags: [\n            '交通',\n            '路口',\n            '城市'\n        ]\n    },\n    {\n        id: 'img_010',\n        name: 'parking_lot.jpg',\n        url: 'https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=800&h=600&fit=crop',\n        width: 800,\n        height: 600,\n        size: 310000,\n        format: 'jpg',\n        datasetId: '2',\n        uploadedAt: new Date('2024-01-18'),\n        tags: [\n            '停车场',\n            '汽车',\n            '建筑'\n        ]\n    }\n];\n// Mock Annotations\nconst mockAnnotations = [\n    {\n        id: '1',\n        taskId: '1',\n        dataItemId: 'img_001',\n        annotatorId: '2',\n        data: {\n            category: '猫',\n            confidence: 0.95,\n            boundingBox: null\n        },\n        status: 'APPROVED',\n        createdAt: new Date('2024-01-20'),\n        updatedAt: new Date('2024-01-21')\n    },\n    {\n        id: '2',\n        taskId: '2',\n        dataItemId: 'img_002',\n        annotatorId: '3',\n        data: {\n            objects: [\n                {\n                    class: '汽车',\n                    bbox: [\n                        100,\n                        100,\n                        200,\n                        200\n                    ],\n                    confidence: 0.88\n                },\n                {\n                    class: '行人',\n                    bbox: [\n                        300,\n                        150,\n                        350,\n                        300\n                    ],\n                    confidence: 0.92\n                }\n            ]\n        },\n        status: 'SUBMITTED',\n        createdAt: new Date('2024-01-19'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '3',\n        taskId: '3',\n        dataItemId: 'audio_001',\n        annotatorId: '4',\n        data: {\n            transcription: '你好，欢迎使用深眸AI标注平台',\n            language: 'zh-CN',\n            confidence: 0.96\n        },\n        status: 'APPROVED',\n        createdAt: new Date('2024-01-18'),\n        updatedAt: new Date('2024-01-19')\n    },\n    {\n        id: '4',\n        taskId: '4',\n        dataItemId: 'text_001',\n        annotatorId: '2',\n        data: {\n            sentiment: 'positive',\n            confidence: 0.87,\n            keywords: [\n                '优秀',\n                '满意',\n                '推荐'\n            ]\n        },\n        status: 'DRAFT',\n        createdAt: new Date('2024-01-17'),\n        updatedAt: new Date('2024-01-18')\n    }\n];\n// Mock Models\nconst mockModels = [\n    {\n        id: '1',\n        name: 'ResNet-50 分类模型',\n        description: '基于ResNet-50架构的图像分类模型，支持10个类别',\n        type: 'CLASSIFICATION',\n        version: 'v1.2.0',\n        status: 'DEPLOYED',\n        projectId: '1',\n        createdBy: '1',\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: 'YOLO-v8 检测模型',\n        description: '用于自动驾驶场景的目标检测模型',\n        type: 'DETECTION',\n        version: 'v2.1.0',\n        status: 'TRAINING',\n        projectId: '2',\n        createdBy: '1',\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: 'Whisper 语音识别',\n        description: '多语言语音识别模型，支持中英文',\n        type: 'NLP',\n        version: 'v1.0.0',\n        status: 'READY',\n        projectId: '3',\n        createdBy: '3',\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: 'BERT 情感分析模型',\n        description: '基于BERT的中文情感分析模型',\n        type: 'NLP',\n        version: 'v1.1.0',\n        status: 'READY',\n        projectId: '4',\n        createdBy: '2',\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    }\n];\n// Helper functions to get related data\nconst getUserById = (id)=>{\n    return mockUsers.find((user)=>user.id === id);\n};\nconst getProjectById = (id)=>{\n    return mockProjects.find((project)=>project.id === id);\n};\nconst getDatasetById = (id)=>{\n    return mockDatasets.find((dataset)=>dataset.id === id);\n};\nconst getTaskById = (id)=>{\n    return mockTasks.find((task)=>task.id === id);\n};\nconst getModelById = (id)=>{\n    return mockModels.find((model)=>model.id === id);\n};\nconst getProjectsByUserId = (userId)=>{\n    return mockProjects.filter((project)=>project.createdBy === userId || project.members.some((member)=>member.userId === userId));\n};\nconst getDatasetsByProjectId = (projectId)=>{\n    return mockDatasets.filter((dataset)=>dataset.projectId === projectId);\n};\nconst getImageById = (id)=>{\n    return mockImages.find((image)=>image.id === id);\n};\nconst getImagesByDatasetId = (datasetId)=>{\n    return mockImages.filter((image)=>image.datasetId === datasetId);\n};\nconst getAllImages = ()=>{\n    return mockImages;\n};\nconst getTasksByProjectId = (projectId)=>{\n    return mockTasks.filter((task)=>task.projectId === projectId);\n};\nconst getTasksByUserId = (userId)=>{\n    return mockTasks.filter((task)=>task.assignedTo.includes(userId));\n};\nconst getAnnotationsByTaskId = (taskId)=>{\n    return mockAnnotations.filter((annotation)=>annotation.taskId === taskId);\n};\nconst getAnnotationsByUserId = (userId)=>{\n    return mockAnnotations.filter((annotation)=>annotation.annotatorId === userId);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./services/mockData.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
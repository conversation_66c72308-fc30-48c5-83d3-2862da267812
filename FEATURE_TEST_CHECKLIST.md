# 🧪 图像标注功能测试清单

## 📋 测试环境
- **URL**: `http://localhost:3000/annotations/image-annotate?datasetId=1`
- **数据集**: 图像分类数据集 (ID: 1)
- **预期图片数量**: 5张图片

## ✅ 功能测试清单

### 1. 页面加载测试
- [ ] 页面正常加载，无白屏或错误
- [ ] 自动加载数据集ID=1的图片
- [ ] 显示第一张图片作为默认选中
- [ ] 布局正确：工具栏(1列) + 画布(3列) + 图片列表(2列)

### 2. 图片列表功能测试
- [ ] 右侧显示"图片列表"面板
- [ ] 显示正确的图片数量 "(5 张)"
- [ ] 每个图片项显示：
  - [ ] 缩略图 (12x12像素)
  - [ ] 图片名称
  - [ ] 图片尺寸 (800 × 600)
  - [ ] 序号 (1, 2, 3, 4, 5)
- [ ] 当前选中图片有蓝色高亮背景
- [ ] 点击其他图片能正确切换
- [ ] 切换图片时清空当前标注

### 3. 实时鼠标坐标测试
- [ ] 鼠标在画布上移动时显示坐标
- [ ] 坐标格式正确：`(x, y)`
- [ ] 坐标跟随鼠标移动
- [ ] 坐标显示位置智能调整：
  - [ ] 默认显示在鼠标右下方
  - [ ] 接近右边界时显示在左侧
  - [ ] 接近上边界时显示在下方
- [ ] 鼠标离开画布时坐标消失
- [ ] 坐标显示样式：黑色半透明背景，白色文字

### 4. 可编辑标签功能测试
- [ ] 创建标注后标签正常显示
- [ ] 选中标注时标签有蓝色背景高亮
- [ ] 双击标签文字进入编辑模式
- [ ] 编辑框正确定位在标签上方
- [ ] 编辑框自动获得焦点
- [ ] 输入新标签名称
- [ ] 按Enter键保存修改
- [ ] 按Escape键取消修改
- [ ] 点击其他地方自动保存
- [ ] 修改后标签立即更新显示

### 5. 图片导航功能测试
- [ ] Header显示当前图片信息
- [ ] 显示图片计数 "(1 / 5)"
- [ ] 左右导航按钮正确显示
- [ ] 第一张图片时左按钮禁用
- [ ] 最后一张图片时右按钮禁用
- [ ] 点击导航按钮正确切换图片
- [ ] 切换图片时标注被清空

### 6. 标注工具集成测试
- [ ] 选择工具正常工作
- [ ] 矩形标注工具正常
- [ ] 圆形标注工具正常
- [ ] 点标注工具正常
- [ ] 标注创建后显示标签
- [ ] 标注选中状态正确
- [ ] 撤销/重做功能正常
- [ ] 保存/清空功能正常

## 🐛 已知问题

### 控制台错误
```
Error: Event handlers cannot be passed to Client Component props.
```
- **状态**: 已知但不影响功能
- **原因**: 可能的Next.js版本兼容性问题
- **影响**: 仅控制台警告，功能正常

## 📊 测试数据

### 数据集1的图片列表
1. **img_001**: cat_sample.jpg (800×600)
2. **img_006**: cat_portrait.jpg (800×600)  
3. **img_007**: dog_playing.jpg (800×600)
4. **img_008**: bird_flying.jpg (800×600)

### 预定义标签
- 人物 (#ef4444 红色)
- 车辆 (#3b82f6 蓝色)
- 建筑 (#10b981 绿色)
- 动物 (#f59e0b 橙色)
- 植物 (#8b5cf6 紫色)
- 物体 (#ec4899 粉色)

## 🎯 测试步骤

### 基础功能测试
1. 打开 `http://localhost:3000/annotations/image-annotate?datasetId=1`
2. 验证页面布局和图片列表
3. 在画布上移动鼠标，观察坐标显示
4. 创建一个矩形标注
5. 双击标签进行编辑
6. 点击图片列表切换图片
7. 使用导航按钮切换图片

### 边界情况测试
1. 在画布边缘移动鼠标测试坐标定位
2. 创建多个标注测试选择和编辑
3. 快速切换图片测试状态清理
4. 测试空标签名称的处理
5. 测试长标签名称的显示

### 性能测试
1. 快速移动鼠标测试坐标更新性能
2. 创建大量标注测试渲染性能
3. 快速切换图片测试内存清理

## 📝 测试报告模板

```
测试日期: ___________
测试人员: ___________
浏览器: ___________

功能测试结果:
□ 图片列表显示: 通过/失败
□ 鼠标坐标跟踪: 通过/失败  
□ 标签编辑功能: 通过/失败
□ 图片导航功能: 通过/失败
□ 标注工具集成: 通过/失败

发现问题:
1. ___________
2. ___________
3. ___________

建议改进:
1. ___________
2. ___________
3. ___________
```

## 🚀 验收标准

所有核心功能必须通过测试：
- ✅ 图片列表正确显示和交互
- ✅ 鼠标坐标实时跟踪
- ✅ 标签双击编辑功能
- ✅ 图片导航和状态管理
- ✅ 与现有标注工具完美集成

满足以上条件即可认为功能实现成功！

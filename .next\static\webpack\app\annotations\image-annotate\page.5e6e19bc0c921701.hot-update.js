"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    const handleContextMenu = (e)=>{\n        var _canvasRef_current;\n        e.preventDefault();\n        const coords = getCanvasCoordinates(e);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // Check if right-clicking on an existing annotation\n        const clickedAnnotation = annotations.find((annotation)=>{\n            switch(annotation.type){\n                case 'rectangle':\n                    const [x, y, width, height] = annotation.coordinates;\n                    return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                case 'circle':\n                    const [cx, cy, radius] = annotation.coordinates;\n                    const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                    return distance <= radius;\n                case 'point':\n                    const [px, py] = annotation.coordinates;\n                    const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                    return pointDistance <= 10;\n                default:\n                    return false;\n            }\n        });\n        setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            targetAnnotation: clickedAnnotation || null,\n            clickPosition: coords\n        });\n    };\n    const hideContextMenu = ()=>{\n        setContextMenu((prev)=>({\n                ...prev,\n                show: false\n            }));\n    };\n    // Context menu actions\n    const handleDeleteAnnotation = ()=>{\n        if (contextMenu.targetAnnotation) {\n            onAnnotationDelete(contextMenu.targetAnnotation.id);\n        }\n        hideContextMenu();\n    };\n    const handleEditLabel = ()=>{\n        if (contextMenu.targetAnnotation) {\n            setEditingLabel(contextMenu.targetAnnotation.id);\n            setEditingLabelValue(contextMenu.targetAnnotation.label);\n        }\n        hideContextMenu();\n    };\n    const handleAddAnnotation = (type)=>{\n        if (!contextMenu.clickPosition) return;\n        const coords = contextMenu.clickPosition;\n        let newAnnotation;\n        if (type === 'point') {\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'rectangle') {\n            // Create a small default rectangle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x - 25,\n                    coords.y - 25,\n                    50,\n                    50\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'circle') {\n            // Create a small default circle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    25\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else {\n            return;\n        }\n        onAnnotationCreate(newAnnotation);\n        hideContextMenu();\n    };\n    const handleChangeLabel = (labelName)=>{\n        if (contextMenu.targetAnnotation) {\n            const labelConfig = predefinedLabels.find((l)=>l.name === labelName);\n            onAnnotationUpdate(contextMenu.targetAnnotation.id, {\n                label: labelName,\n                color: (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || currentColor\n            });\n        }\n        hideContextMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 478,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 520,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 519,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 473,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"fbV+AjM4vIIwRHQRKPy9Hvtsk98=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
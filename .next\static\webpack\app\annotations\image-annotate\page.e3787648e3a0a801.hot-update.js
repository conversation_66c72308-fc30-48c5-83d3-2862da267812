"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseUp\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 318,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"YTGJpgkBX9L8LqWTy4YRdKpxjIg=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    const handleContextMenu = (e)=>{\n        var _canvasRef_current;\n        e.preventDefault();\n        const coords = getCanvasCoordinates(e);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // Check if right-clicking on an existing annotation\n        const clickedAnnotation = annotations.find((annotation)=>{\n            switch(annotation.type){\n                case 'rectangle':\n                    const [x, y, width, height] = annotation.coordinates;\n                    return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                case 'circle':\n                    const [cx, cy, radius] = annotation.coordinates;\n                    const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                    return distance <= radius;\n                case 'point':\n                    const [px, py] = annotation.coordinates;\n                    const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                    return pointDistance <= 10;\n                default:\n                    return false;\n            }\n        });\n        setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            targetAnnotation: clickedAnnotation || null,\n            clickPosition: coords\n        });\n    };\n    const hideContextMenu = ()=>{\n        setContextMenu((prev)=>({\n                ...prev,\n                show: false\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 411,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 406,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"fbV+AjM4vIIwRHQRKPy9Hvtsk98=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
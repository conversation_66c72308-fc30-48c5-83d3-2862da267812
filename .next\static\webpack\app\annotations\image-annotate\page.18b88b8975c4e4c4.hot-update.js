"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./app/annotations/image-annotate/page.tsx":
/*!*************************************************!*\
  !*** ./app/annotations/image-annotate/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageAnnotatePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/routes */ \"(app-pages-browser)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/annotation/image-annotation-canvas */ \"(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\");\n/* harmony import */ var _components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/annotation/annotation-toolbar */ \"(app-pages-browser)/./components/annotation/annotation-toolbar.tsx\");\n/* harmony import */ var _components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/annotation/annotation-list */ \"(app-pages-browser)/./components/annotation/annotation-list.tsx\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ImageAnnotatePage() {\n    _s();\n    const [currentTool, setCurrentTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [annotations, setAnnotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnnotation, setSelectedAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentLabel, setCurrentLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showLabels, setShowLabels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageList, setImageList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [datasetId, setDatasetId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Load image from URL params or show selector\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotatePage.useEffect\": ()=>{\n            const imageId = searchParams.get('imageId');\n            const urlDatasetId = searchParams.get('datasetId');\n            if (urlDatasetId) {\n                setDatasetId(urlDatasetId);\n                loadDatasetImages(urlDatasetId);\n            } else if (imageId) {\n                loadImageById(imageId);\n            } else {\n                // Load a default image for immediate use\n                loadDefaultImage();\n            }\n        }\n    }[\"ImageAnnotatePage.useEffect\"], [\n        searchParams\n    ]);\n    const loadDefaultImage = async ()=>{\n        setLoading(true);\n        try {\n            console.log('Loading default image...');\n            // Load the first available image as default\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage();\n            console.log('Default image loaded:', image);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            } else {\n                console.log('No images available, showing selector');\n                // If no images available, show selector\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load default image:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadImageById = async (imageId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getImage(imageId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDatasetImages = async (datasetId)=>{\n        setLoading(true);\n        try {\n            console.log('Loading images from dataset:', datasetId);\n            const images = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getImagesByDataset(datasetId);\n            console.log('Loaded images:', images);\n            if (images.length > 0) {\n                setImageList(images);\n                setCurrentImageIndex(0);\n                setSelectedImage(images[0]);\n                setShowImageSelector(false);\n            } else {\n                console.log('No images found in dataset, showing selector');\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load dataset images:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRandomImageFromDataset = async (datasetId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage(datasetId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load random image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Predefined labels with colors\n    const predefinedLabels = [\n        {\n            name: '人物',\n            color: '#ef4444'\n        },\n        {\n            name: '车辆',\n            color: '#3b82f6'\n        },\n        {\n            name: '建筑',\n            color: '#10b981'\n        },\n        {\n            name: '动物',\n            color: '#f59e0b'\n        },\n        {\n            name: '植物',\n            color: '#8b5cf6'\n        },\n        {\n            name: '物体',\n            color: '#ec4899'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    // Event handlers\n    const handleAnnotationCreate = (annotation)=>{\n        setAnnotations((prev)=>[\n                ...prev,\n                annotation\n            ]);\n        saveToHistory([\n            ...annotations,\n            annotation\n        ]);\n    };\n    const handleAnnotationUpdate = (id, updates)=>{\n        setAnnotations((prev)=>prev.map((ann)=>ann.id === id ? {\n                    ...ann,\n                    ...updates\n                } : ann));\n    };\n    const handleAnnotationDelete = (id)=>{\n        setAnnotations((prev)=>prev.filter((ann)=>ann.id !== id));\n        setSelectedAnnotation(null);\n        saveToHistory(annotations.filter((ann)=>ann.id !== id));\n    };\n    const handleAnnotationVisibilityToggle = (id)=>{\n        // For now, just select/deselect the annotation\n        setSelectedAnnotation(selectedAnnotation === id ? null : id);\n    };\n    const saveToHistory = (newAnnotations)=>{\n        const annotationsToSave = newAnnotations || annotations;\n        const newHistory = history.slice(0, historyIndex + 1);\n        newHistory.push([\n            ...annotationsToSave\n        ]);\n        setHistory(newHistory);\n        setHistoryIndex(newHistory.length - 1);\n    };\n    const handleUndo = ()=>{\n        if (historyIndex > 0) {\n            setHistoryIndex(historyIndex - 1);\n            setAnnotations([\n                ...history[historyIndex - 1]\n            ]);\n        }\n    };\n    const handleRedo = ()=>{\n        if (historyIndex < history.length - 1) {\n            setHistoryIndex(historyIndex + 1);\n            setAnnotations([\n                ...history[historyIndex + 1]\n            ]);\n        }\n    };\n    const handleClear = ()=>{\n        if (window.confirm('确定要清空所有标注吗？')) {\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n            saveToHistory([]);\n        }\n    };\n    const handleSave = ()=>{\n        // Save annotations logic here\n        console.log('Saving annotations:', annotations);\n        alert(\"已保存 \".concat(annotations.length, \" 个标注\"));\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.ANNOTATIONS);\n    };\n    // Navigation functions\n    const goToPreviousImage = ()=>{\n        if (imageList.length > 0 && currentImageIndex > 0) {\n            const newIndex = currentImageIndex - 1;\n            setCurrentImageIndex(newIndex);\n            setSelectedImage(imageList[newIndex]);\n            // Clear annotations when switching images\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n        }\n    };\n    const goToNextImage = ()=>{\n        if (imageList.length > 0 && currentImageIndex < imageList.length - 1) {\n            const newIndex = currentImageIndex + 1;\n            setCurrentImageIndex(newIndex);\n            setSelectedImage(imageList[newIndex]);\n            // Clear annotations when switching images\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n        }\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载图像...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    // Show image selector if no image is selected\n    if (showImageSelector || !selectedImage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"选择要标注的图像\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"选择图像进行标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"点击下方图像开始标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        {\n                                            id: 'img_001',\n                                            name: '猫咪图片',\n                                            url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_002',\n                                            name: '街景图片',\n                                            url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_003',\n                                            name: '狗狗图片',\n                                            url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_004',\n                                            name: '建筑图片',\n                                            url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_005',\n                                            name: '风景图片',\n                                            url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'\n                                        }\n                                    ].map((img)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"cursor-pointer rounded-lg border-2 border-gray-200 hover:border-blue-500 transition-colors\",\n                                            onClick: ()=>{\n                                                setSelectedImage({\n                                                    id: img.id,\n                                                    name: img.name,\n                                                    url: img.url,\n                                                    width: 800,\n                                                    height: 600,\n                                                    size: 200000,\n                                                    format: 'jpg',\n                                                    datasetId: '1',\n                                                    uploadedAt: new Date(),\n                                                    tags: []\n                                                });\n                                                setShowImageSelector(false);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: img.url,\n                                                        alt: img.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: img.name\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, img.id, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: [\n                                            selectedImage.name,\n                                            imageList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm\",\n                                                children: [\n                                                    \"(\",\n                                                    currentImageIndex + 1,\n                                                    \" / \",\n                                                    imageList.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            imageList.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center justify-center h-9 w-9 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: goToPreviousImage,\n                                        disabled: currentImageIndex === 0,\n                                        title: \"上一张图片\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center justify-center h-9 w-9 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: goToNextImage,\n                                        disabled: currentImageIndex === imageList.length - 1,\n                                        title: \"下一张图片\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-9 rounded-md px-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground text-sm\",\n                                onClick: ()=>setShowImageSelector(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"切换图像\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-5 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__.AnnotationToolbar, {\n                                currentTool: currentTool,\n                                onToolChange: setCurrentTool,\n                                currentLabel: currentLabel,\n                                onLabelChange: setCurrentLabel,\n                                predefinedLabels: predefinedLabels,\n                                zoom: zoom,\n                                onZoomChange: setZoom,\n                                showLabels: showLabels,\n                                onShowLabelsToggle: ()=>setShowLabels(!showLabels),\n                                canUndo: historyIndex > 0,\n                                canRedo: historyIndex < history.length - 1,\n                                onUndo: handleUndo,\n                                onRedo: handleRedo,\n                                onSave: handleSave,\n                                onClear: handleClear\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__.AnnotationList, {\n                                annotations: annotations,\n                                selectedAnnotation: selectedAnnotation,\n                                onAnnotationSelect: setSelectedAnnotation,\n                                onAnnotationDelete: handleAnnotationDelete,\n                                onAnnotationUpdate: handleAnnotationUpdate,\n                                onAnnotationVisibilityToggle: handleAnnotationVisibilityToggle\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"图像标注区域\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                \"使用左侧工具在图像上创建标注 - 当前工具: \",\n                                                currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-auto border rounded-lg bg-gray-50\",\n                                            style: {\n                                                maxHeight: '600px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"scale(\".concat(zoom, \")\"),\n                                                    transformOrigin: 'top left'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__.ImageAnnotationCanvas, {\n                                                    imageUrl: selectedImage.url,\n                                                    imageWidth: selectedImage.width,\n                                                    imageHeight: selectedImage.height,\n                                                    annotations: annotations,\n                                                    selectedAnnotation: selectedAnnotation,\n                                                    currentTool: currentTool,\n                                                    currentLabel: currentLabel,\n                                                    currentColor: getCurrentColor(),\n                                                    zoom: zoom,\n                                                    showLabels: showLabels,\n                                                    onAnnotationCreate: handleAnnotationCreate,\n                                                    onAnnotationUpdate: handleAnnotationUpdate,\n                                                    onAnnotationSelect: setSelectedAnnotation\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"使用说明:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 选择工具和标签后，在图像上拖拽创建标注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 点击标注可以选中，在右侧列表中编辑标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 使用缩放工具可以放大查看细节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 支持撤销/重做操作，可以随时恢复之前的状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"总标注数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.filter((a)=>a.label).length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"已标记\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                Math.round(zoom * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"缩放比例\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: history.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"历史记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-yellow-900 mb-2\",\n                                                    children: \"调试信息:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-yellow-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 选中图像: \",\n                                                                selectedImage ? selectedImage.name : '无'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 图像URL: \",\n                                                                selectedImage ? selectedImage.url : '无'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 显示选择器: \",\n                                                                showImageSelector ? '是' : '否'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 加载状态: \",\n                                                                loading ? '加载中' : '已完成'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotatePage, \"a2yQA7zQdP8b4h9Tcj7ru6mVVcY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ImageAnnotatePage;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hbm5vdGF0aW9ucy9pbWFnZS1hbm5vdGF0ZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNpQjtBQUNvQztBQUNyRDtBQUM0QztBQUN3QjtBQUNWO0FBQ3RCO0FBRXpCO0FBRXZDLFNBQVNtQjs7SUFDdEIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdyQiwrQ0FBUUEsQ0FBaUI7SUFDL0QsTUFBTSxDQUFDc0IsYUFBYUMsZUFBZSxHQUFHdkIsK0NBQVFBLENBQW9CLEVBQUU7SUFDcEUsTUFBTSxDQUFDd0Isb0JBQW9CQyxzQkFBc0IsR0FBR3pCLCtDQUFRQSxDQUFnQjtJQUM1RSxNQUFNLENBQUMwQixjQUFjQyxnQkFBZ0IsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRCLE1BQU1DLFFBQVEsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQ2pDLE1BQU0sQ0FBQzhCLFlBQVlDLGNBQWMsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2dDLFNBQVNDLFdBQVcsR0FBR2pDLCtDQUFRQSxDQUFzQixFQUFFO0lBQzlELE1BQU0sQ0FBQ2tDLGNBQWNDLGdCQUFnQixHQUFHbkMsK0NBQVFBLENBQUMsQ0FBQztJQUNsRCxNQUFNLENBQUNvQyxlQUFlQyxpQkFBaUIsR0FBR3JDLCtDQUFRQSxDQUFtQjtJQUNyRSxNQUFNLENBQUNzQyxXQUFXQyxhQUFhLEdBQUd2QywrQ0FBUUEsQ0FBYyxFQUFFO0lBQzFELE1BQU0sQ0FBQ3dDLG1CQUFtQkMscUJBQXFCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUMwQyxXQUFXQyxhQUFhLEdBQUczQywrQ0FBUUEsQ0FBZ0I7SUFDMUQsTUFBTSxDQUFDNEMsbUJBQW1CQyxxQkFBcUIsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzhDLFNBQVNDLFdBQVcsR0FBRy9DLCtDQUFRQSxDQUFDO0lBRXZDLE1BQU1nRCxTQUFTOUMsMERBQVNBO0lBQ3hCLE1BQU0rQyxlQUFlOUMsZ0VBQWVBO0lBRXBDLDhDQUE4QztJQUM5Q0YsZ0RBQVNBO3VDQUFDO1lBQ1IsTUFBTWlELFVBQVVELGFBQWFFLEdBQUcsQ0FBQztZQUNqQyxNQUFNQyxlQUFlSCxhQUFhRSxHQUFHLENBQUM7WUFFdEMsSUFBSUMsY0FBYztnQkFDaEJULGFBQWFTO2dCQUNiQyxrQkFBa0JEO1lBQ3BCLE9BQU8sSUFBSUYsU0FBUztnQkFDbEJJLGNBQWNKO1lBQ2hCLE9BQU87Z0JBQ0wseUNBQXlDO2dCQUN6Q0s7WUFDRjtRQUNGO3NDQUFHO1FBQUNOO0tBQWE7SUFFakIsTUFBTU0sbUJBQW1CO1FBQ3ZCUixXQUFXO1FBQ1gsSUFBSTtZQUNGUyxRQUFRQyxHQUFHLENBQUM7WUFDWiw0Q0FBNEM7WUFDNUMsTUFBTUMsUUFBUSxNQUFNeEMsZ0VBQVlBLENBQUN5QyxjQUFjO1lBQy9DSCxRQUFRQyxHQUFHLENBQUMseUJBQXlCQztZQUNyQyxJQUFJQSxPQUFPO2dCQUNUckIsaUJBQWlCcUI7Z0JBQ2pCYixxQkFBcUI7WUFDdkIsT0FBTztnQkFDTFcsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLHdDQUF3QztnQkFDeENaLHFCQUFxQjtZQUN2QjtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQ2YscUJBQXFCO1FBQ3ZCLFNBQVU7WUFDUkUsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNTyxnQkFBZ0IsT0FBT0o7UUFDM0JILFdBQVc7UUFDWCxJQUFJO1lBQ0YsTUFBTVcsUUFBUSxNQUFNeEMsZ0VBQVlBLENBQUMyQyxRQUFRLENBQUNYO1lBQzFDLElBQUlRLE9BQU87Z0JBQ1RyQixpQkFBaUJxQjtnQkFDakJiLHFCQUFxQjtZQUN2QjtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMseUJBQXlCQTtRQUN6QyxTQUFVO1lBQ1JiLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTU0sb0JBQW9CLE9BQU9YO1FBQy9CSyxXQUFXO1FBQ1gsSUFBSTtZQUNGUyxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDZjtZQUM1QyxNQUFNb0IsU0FBUyxNQUFNNUMsZ0VBQVlBLENBQUM2QyxrQkFBa0IsQ0FBQ3JCO1lBQ3JEYyxRQUFRQyxHQUFHLENBQUMsa0JBQWtCSztZQUU5QixJQUFJQSxPQUFPRSxNQUFNLEdBQUcsR0FBRztnQkFDckJ6QixhQUFhdUI7Z0JBQ2JyQixxQkFBcUI7Z0JBQ3JCSixpQkFBaUJ5QixNQUFNLENBQUMsRUFBRTtnQkFDMUJqQixxQkFBcUI7WUFDdkIsT0FBTztnQkFDTFcsUUFBUUMsR0FBRyxDQUFDO2dCQUNaWixxQkFBcUI7WUFDdkI7UUFDRixFQUFFLE9BQU9lLE9BQU87WUFDZEosUUFBUUksS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaERmLHFCQUFxQjtRQUN2QixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWtCLDZCQUE2QixPQUFPdkI7UUFDeENLLFdBQVc7UUFDWCxJQUFJO1lBQ0YsTUFBTVcsUUFBUSxNQUFNeEMsZ0VBQVlBLENBQUN5QyxjQUFjLENBQUNqQjtZQUNoRCxJQUFJZ0IsT0FBTztnQkFDVHJCLGlCQUFpQnFCO2dCQUNqQmIscUJBQXFCO1lBQ3ZCO1FBQ0YsRUFBRSxPQUFPZSxPQUFPO1lBQ2RKLFFBQVFJLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQ2hELFNBQVU7WUFDUmIsV0FBVztRQUNiO0lBQ0Y7SUFJQSxnQ0FBZ0M7SUFDaEMsTUFBTW1CLG1CQUFtQjtRQUN2QjtZQUFFQyxNQUFNO1lBQU1DLE9BQU87UUFBVTtRQUMvQjtZQUFFRCxNQUFNO1lBQU1DLE9BQU87UUFBVTtRQUMvQjtZQUFFRCxNQUFNO1lBQU1DLE9BQU87UUFBVTtRQUMvQjtZQUFFRCxNQUFNO1lBQU1DLE9BQU87UUFBVTtRQUMvQjtZQUFFRCxNQUFNO1lBQU1DLE9BQU87UUFBVTtRQUMvQjtZQUFFRCxNQUFNO1lBQU1DLE9BQU87UUFBVTtLQUNoQztJQUVELE1BQU1DLGtCQUFrQjtRQUN0QixNQUFNQyxjQUFjSixpQkFBaUJLLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUwsSUFBSSxLQUFLekM7UUFDMUQsT0FBTzRDLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYUYsS0FBSyxLQUFJO0lBQy9CO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU1LLHlCQUF5QixDQUFDQztRQUM5Qm5ELGVBQWVvRCxDQUFBQSxPQUFRO21CQUFJQTtnQkFBTUQ7YUFBVztRQUM1Q0UsY0FBYztlQUFJdEQ7WUFBYW9EO1NBQVc7SUFDNUM7SUFFQSxNQUFNRyx5QkFBeUIsQ0FBQ0MsSUFBWUM7UUFDMUN4RCxlQUFlb0QsQ0FBQUEsT0FBUUEsS0FBS0ssR0FBRyxDQUFDQyxDQUFBQSxNQUM5QkEsSUFBSUgsRUFBRSxLQUFLQSxLQUFLO29CQUFFLEdBQUdHLEdBQUc7b0JBQUUsR0FBR0YsT0FBTztnQkFBQyxJQUFJRTtJQUU3QztJQUVBLE1BQU1DLHlCQUF5QixDQUFDSjtRQUM5QnZELGVBQWVvRCxDQUFBQSxPQUFRQSxLQUFLUSxNQUFNLENBQUNGLENBQUFBLE1BQU9BLElBQUlILEVBQUUsS0FBS0E7UUFDckRyRCxzQkFBc0I7UUFDdEJtRCxjQUFjdEQsWUFBWTZELE1BQU0sQ0FBQ0YsQ0FBQUEsTUFBT0EsSUFBSUgsRUFBRSxLQUFLQTtJQUNyRDtJQUVBLE1BQU1NLG1DQUFtQyxDQUFDTjtRQUN4QywrQ0FBK0M7UUFDL0NyRCxzQkFBc0JELHVCQUF1QnNELEtBQUssT0FBT0E7SUFDM0Q7SUFFQSxNQUFNRixnQkFBZ0IsQ0FBQ1M7UUFDckIsTUFBTUMsb0JBQW9CRCxrQkFBa0IvRDtRQUM1QyxNQUFNaUUsYUFBYXZELFFBQVF3RCxLQUFLLENBQUMsR0FBR3RELGVBQWU7UUFDbkRxRCxXQUFXRSxJQUFJLENBQUM7ZUFBSUg7U0FBa0I7UUFDdENyRCxXQUFXc0Q7UUFDWHBELGdCQUFnQm9ELFdBQVd2QixNQUFNLEdBQUc7SUFDdEM7SUFFQSxNQUFNMEIsYUFBYTtRQUNqQixJQUFJeEQsZUFBZSxHQUFHO1lBQ3BCQyxnQkFBZ0JELGVBQWU7WUFDL0JYLGVBQWU7bUJBQUlTLE9BQU8sQ0FBQ0UsZUFBZSxFQUFFO2FBQUM7UUFDL0M7SUFDRjtJQUVBLE1BQU15RCxhQUFhO1FBQ2pCLElBQUl6RCxlQUFlRixRQUFRZ0MsTUFBTSxHQUFHLEdBQUc7WUFDckM3QixnQkFBZ0JELGVBQWU7WUFDL0JYLGVBQWU7bUJBQUlTLE9BQU8sQ0FBQ0UsZUFBZSxFQUFFO2FBQUM7UUFDL0M7SUFDRjtJQUVBLE1BQU0wRCxjQUFjO1FBQ2xCLElBQUlDLE9BQU9DLE9BQU8sQ0FBQyxnQkFBZ0I7WUFDakN2RSxlQUFlLEVBQUU7WUFDakJFLHNCQUFzQjtZQUN0Qm1ELGNBQWMsRUFBRTtRQUNsQjtJQUNGO0lBRUEsTUFBTW1CLGFBQWE7UUFDakIsOEJBQThCO1FBQzlCdkMsUUFBUUMsR0FBRyxDQUFDLHVCQUF1Qm5DO1FBQ25DMEUsTUFBTSxPQUEwQixPQUFuQjFFLFlBQVkwQyxNQUFNLEVBQUM7UUFDaENoQixPQUFPeUMsSUFBSSxDQUFDaEYscURBQU1BLENBQUN3RixXQUFXO0lBQ2hDO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1DLG9CQUFvQjtRQUN4QixJQUFJNUQsVUFBVTBCLE1BQU0sR0FBRyxLQUFLeEIsb0JBQW9CLEdBQUc7WUFDakQsTUFBTTJELFdBQVczRCxvQkFBb0I7WUFDckNDLHFCQUFxQjBEO1lBQ3JCOUQsaUJBQWlCQyxTQUFTLENBQUM2RCxTQUFTO1lBQ3BDLDBDQUEwQztZQUMxQzVFLGVBQWUsRUFBRTtZQUNqQkUsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFNMkUsZ0JBQWdCO1FBQ3BCLElBQUk5RCxVQUFVMEIsTUFBTSxHQUFHLEtBQUt4QixvQkFBb0JGLFVBQVUwQixNQUFNLEdBQUcsR0FBRztZQUNwRSxNQUFNbUMsV0FBVzNELG9CQUFvQjtZQUNyQ0MscUJBQXFCMEQ7WUFDckI5RCxpQkFBaUJDLFNBQVMsQ0FBQzZELFNBQVM7WUFDcEMsMENBQTBDO1lBQzFDNUUsZUFBZSxFQUFFO1lBQ2pCRSxzQkFBc0I7UUFDeEI7SUFDRjtJQUVBLHFCQUFxQjtJQUNyQixJQUFJcUIsU0FBUztRQUNYLHFCQUNFLDhEQUFDdUQ7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsOENBQThDO0lBQzlDLElBQUkxRCxxQkFBcUIsQ0FBQ1IsZUFBZTtRQUN2QyxxQkFDRSw4REFBQ2lFO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FDQ0YsV0FBVTtnQ0FDVkcsU0FBUyxJQUFNekQsT0FBTzBELElBQUk7MENBRTFCLDRFQUFDaEcsb0hBQVNBO29DQUFDNEYsV0FBVTs7Ozs7Ozs7Ozs7MENBRXZCLDhEQUFDRDs7a0RBQ0MsOERBQUNNO3dDQUFHTCxXQUFVO2tEQUFtQzs7Ozs7O2tEQUNqRCw4REFBQ0M7d0NBQUVELFdBQVU7a0RBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNeEMsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDbEcscURBQUlBOzswQ0FDSCw4REFBQ0csMkRBQVVBOztrREFDVCw4REFBQ0MsMERBQVNBO2tEQUFDOzs7Ozs7a0RBQ1gsOERBQUNGLGdFQUFlQTtrREFBQzs7Ozs7Ozs7Ozs7OzBDQUVuQiw4REFBQ0QsNERBQVdBOzBDQUNWLDRFQUFDZ0c7b0NBQUlDLFdBQVU7OENBQ1o7d0NBQ0M7NENBQUV4QixJQUFJOzRDQUFXWCxNQUFNOzRDQUFReUMsS0FBSzt3Q0FBb0Y7d0NBQ3hIOzRDQUFFOUIsSUFBSTs0Q0FBV1gsTUFBTTs0Q0FBUXlDLEtBQUs7d0NBQW9GO3dDQUN4SDs0Q0FBRTlCLElBQUk7NENBQVdYLE1BQU07NENBQVF5QyxLQUFLO3dDQUFpRjt3Q0FDckg7NENBQUU5QixJQUFJOzRDQUFXWCxNQUFNOzRDQUFReUMsS0FBSzt3Q0FBb0Y7d0NBQ3hIOzRDQUFFOUIsSUFBSTs0Q0FBV1gsTUFBTTs0Q0FBUXlDLEtBQUs7d0NBQW9GO3FDQUN6SCxDQUFDNUIsR0FBRyxDQUFDLENBQUM2QixvQkFDTCw4REFBQ1I7NENBRUNDLFdBQVU7NENBQ1ZHLFNBQVM7Z0RBQ1BwRSxpQkFBaUI7b0RBQ2Z5QyxJQUFJK0IsSUFBSS9CLEVBQUU7b0RBQ1ZYLE1BQU0wQyxJQUFJMUMsSUFBSTtvREFDZHlDLEtBQUtDLElBQUlELEdBQUc7b0RBQ1pFLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JDLE1BQU07b0RBQ05DLFFBQVE7b0RBQ1J2RSxXQUFXO29EQUNYd0UsWUFBWSxJQUFJQztvREFDaEJDLE1BQU0sRUFBRTtnREFDVjtnREFDQXZFLHFCQUFxQjs0Q0FDdkI7OzhEQUVBLDhEQUFDd0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNPO3dEQUNDUSxLQUFLUixJQUFJRCxHQUFHO3dEQUNaVSxLQUFLVCxJQUFJMUMsSUFBSTt3REFDYm1DLFdBQVU7Ozs7Ozs7Ozs7OzhEQUdkLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7a0VBQXVCTyxJQUFJMUMsSUFBSTs7Ozs7Ozs7Ozs7OzJDQTFCM0MwQyxJQUFJL0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFvQzdCO0lBRUEscUJBQ0UsOERBQUN1QjtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUNDRixXQUFVO2dDQUNWRyxTQUFTLElBQU16RCxPQUFPMEQsSUFBSTswQ0FFMUIsNEVBQUNoRyxvSEFBU0E7b0NBQUM0RixXQUFVOzs7Ozs7Ozs7OzswQ0FFdkIsOERBQUNEOztrREFDQyw4REFBQ007d0NBQUdMLFdBQVU7a0RBQW1DOzs7Ozs7a0RBQ2pELDhEQUFDQzt3Q0FBRUQsV0FBVTs7NENBQ1ZsRSxjQUFjK0IsSUFBSTs0Q0FDbEI3QixVQUFVMEIsTUFBTSxHQUFHLG1CQUNsQiw4REFBQ3VEO2dEQUFLakIsV0FBVTs7b0RBQWU7b0RBQzNCOUQsb0JBQW9CO29EQUFFO29EQUFJRixVQUFVMEIsTUFBTTtvREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNdkQsOERBQUNxQzt3QkFBSUMsV0FBVTs7NEJBRVpoRSxVQUFVMEIsTUFBTSxHQUFHLG1CQUNsQjs7a0RBQ0UsOERBQUN3Qzt3Q0FDQ0YsV0FBVTt3Q0FDVkcsU0FBU1A7d0NBQ1RzQixVQUFVaEYsc0JBQXNCO3dDQUNoQ2lGLE9BQU07a0RBRU4sNEVBQUM1RyxxSEFBV0E7NENBQUN5RixXQUFVOzs7Ozs7Ozs7OztrREFFekIsOERBQUNFO3dDQUNDRixXQUFVO3dDQUNWRyxTQUFTTDt3Q0FDVG9CLFVBQVVoRixzQkFBc0JGLFVBQVUwQixNQUFNLEdBQUc7d0NBQ25EeUQsT0FBTTtrREFFTiw0RUFBQzNHLHFIQUFZQTs0Q0FBQ3dGLFdBQVU7Ozs7Ozs7Ozs7Ozs7MENBSTlCLDhEQUFDRTtnQ0FDQ0YsV0FBVTtnQ0FDVkcsU0FBUyxJQUFNNUQscUJBQXFCOztrREFFcEMsOERBQUNqQyxxSEFBU0E7d0NBQUMwRixXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU01Qyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUN0Rix3RkFBaUJBO2dDQUNoQkksYUFBYUE7Z0NBQ2JzRyxjQUFjckc7Z0NBQ2RLLGNBQWNBO2dDQUNkaUcsZUFBZWhHO2dDQUNmdUMsa0JBQWtCQTtnQ0FDbEJ0QyxNQUFNQTtnQ0FDTmdHLGNBQWMvRjtnQ0FDZEMsWUFBWUE7Z0NBQ1orRixvQkFBb0IsSUFBTTlGLGNBQWMsQ0FBQ0Q7Z0NBQ3pDZ0csU0FBUzVGLGVBQWU7Z0NBQ3hCNkYsU0FBUzdGLGVBQWVGLFFBQVFnQyxNQUFNLEdBQUc7Z0NBQ3pDZ0UsUUFBUXRDO2dDQUNSdUMsUUFBUXRDO2dDQUNSdUMsUUFBUW5DO2dDQUNSb0MsU0FBU3ZDOzs7Ozs7MENBR1gsOERBQUMzRSxrRkFBY0E7Z0NBQ2JLLGFBQWFBO2dDQUNiRSxvQkFBb0JBO2dDQUNwQjRHLG9CQUFvQjNHO2dDQUNwQjRHLG9CQUFvQm5EO2dDQUNwQm9ELG9CQUFvQnpEO2dDQUNwQjBELDhCQUE4Qm5EOzs7Ozs7Ozs7Ozs7a0NBS2xDLDhEQUFDaUI7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNsRyxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7O3NEQUNULDhEQUFDQywwREFBU0E7c0RBQUM7Ozs7OztzREFDWCw4REFBQ0YsZ0VBQWVBOztnREFBQztnREFDU2MsZ0JBQWdCLFdBQVcsT0FBT0EsZ0JBQWdCLGNBQWMsT0FBT0EsZ0JBQWdCLFdBQVcsT0FBT0EsZ0JBQWdCLFVBQVUsUUFBUTs7Ozs7Ozs7Ozs7Ozs4Q0FHdkssOERBQUNmLDREQUFXQTs7c0RBQ1YsOERBQUNnRzs0Q0FDQ0MsV0FBVTs0Q0FDVmtDLE9BQU87Z0RBQUVDLFdBQVc7NENBQVE7c0RBRTVCLDRFQUFDcEM7Z0RBQUltQyxPQUFPO29EQUFFRSxXQUFXLFNBQWMsT0FBTDlHLE1BQUs7b0RBQUkrRyxpQkFBaUI7Z0RBQVc7MERBQ3JFLDRFQUFDNUgsaUdBQXFCQTtvREFDcEI2SCxVQUFVeEcsY0FBY3dFLEdBQUc7b0RBQzNCaUMsWUFBWXpHLGNBQWMwRSxLQUFLO29EQUMvQmdDLGFBQWExRyxjQUFjMkUsTUFBTTtvREFDakN6RixhQUFhQTtvREFDYkUsb0JBQW9CQTtvREFDcEJKLGFBQWFBO29EQUNiTSxjQUFjQTtvREFDZHFILGNBQWMxRTtvREFDZHpDLE1BQU1BO29EQUNORSxZQUFZQTtvREFDWmtILG9CQUFvQnZFO29EQUNwQjZELG9CQUFvQnpEO29EQUNwQnVELG9CQUFvQjNHOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUsxQiw4REFBQzRFOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzJDO29EQUFHM0MsV0FBVTs4REFBeUM7Ozs7Ozs4REFDdkQsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0M7c0VBQUU7Ozs7OztzRUFDSCw4REFBQ0E7c0VBQUU7Ozs7OztzRUFDSCw4REFBQ0E7c0VBQUU7Ozs7OztzRUFDSCw4REFBQ0E7c0VBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLUCw4REFBQ0Y7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFvQ2hGLFlBQVkwQyxNQUFNOzs7Ozs7c0VBQ3JFLDhEQUFDcUM7NERBQUlDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBRXpDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFvQ2hGLFlBQVk2RCxNQUFNLENBQUMrRCxDQUFBQSxJQUFLQSxFQUFFQyxLQUFLLEVBQUVuRixNQUFNOzs7Ozs7c0VBQzFGLDhEQUFDcUM7NERBQUlDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBRXpDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOztnRUFBb0M4QyxLQUFLQyxLQUFLLENBQUN6SCxPQUFPO2dFQUFLOzs7Ozs7O3NFQUMxRSw4REFBQ3lFOzREQUFJQyxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7OzhEQUV6Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBb0N0RSxRQUFRZ0MsTUFBTTs7Ozs7O3NFQUNqRSw4REFBQ3FDOzREQUFJQyxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUszQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDMkM7b0RBQUczQyxXQUFVOzhEQUEyQzs7Ozs7OzhEQUN6RCw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDQzs7Z0VBQUU7Z0VBQVNuRSxnQkFBZ0JBLGNBQWMrQixJQUFJLEdBQUc7Ozs7Ozs7c0VBQ2pELDhEQUFDb0M7O2dFQUFFO2dFQUFVbkUsZ0JBQWdCQSxjQUFjd0UsR0FBRyxHQUFHOzs7Ozs7O3NFQUNqRCw4REFBQ0w7O2dFQUFFO2dFQUFVM0Qsb0JBQW9CLE1BQU07Ozs7Ozs7c0VBQ3ZDLDhEQUFDMkQ7O2dFQUFFO2dFQUFTekQsVUFBVSxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTaEQ7R0FoZHdCM0I7O1FBZ0JQakIsc0RBQVNBO1FBQ0hDLDREQUFlQTs7O0tBakJkZ0IiLCJzb3VyY2VzIjpbIkY6XFxkZWVwc2lnaHRcXGZyb25ldGVuZFxcYXBwXFxhbm5vdGF0aW9uc1xcaW1hZ2UtYW5ub3RhdGVcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgUk9VVEVTIH0gZnJvbSAnQC9jb25zdGFudHMvcm91dGVzJ1xuaW1wb3J0IHsgQXJyb3dMZWZ0LCBJbWFnZSBhcyBJbWFnZUljb24sIENoZXZyb25MZWZ0LCBDaGV2cm9uUmlnaHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBJbWFnZUFubm90YXRpb25DYW52YXMsIEFubm90YXRpb25TaGFwZSB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvYW5ub3RhdGlvbi9pbWFnZS1hbm5vdGF0aW9uLWNhbnZhcydcbmltcG9ydCB7IEFubm90YXRpb25Ub29sYmFyLCBBbm5vdGF0aW9uVG9vbCB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvYW5ub3RhdGlvbi9hbm5vdGF0aW9uLXRvb2xiYXInXG5pbXBvcnQgeyBBbm5vdGF0aW9uTGlzdCB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvYW5ub3RhdGlvbi9hbm5vdGF0aW9uLWxpc3QnXG5pbXBvcnQgeyBJbWFnZURhdGEgfSBmcm9tICdAL3NlcnZpY2VzL21vY2tEYXRhJ1xuaW1wb3J0IHsgaW1hZ2VTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9pbWFnZVNlcnZpY2UnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEltYWdlQW5ub3RhdGVQYWdlKCkge1xuICBjb25zdCBbY3VycmVudFRvb2wsIHNldEN1cnJlbnRUb29sXSA9IHVzZVN0YXRlPEFubm90YXRpb25Ub29sPignc2VsZWN0JylcbiAgY29uc3QgW2Fubm90YXRpb25zLCBzZXRBbm5vdGF0aW9uc10gPSB1c2VTdGF0ZTxBbm5vdGF0aW9uU2hhcGVbXT4oW10pXG4gIGNvbnN0IFtzZWxlY3RlZEFubm90YXRpb24sIHNldFNlbGVjdGVkQW5ub3RhdGlvbl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbY3VycmVudExhYmVsLCBzZXRDdXJyZW50TGFiZWxdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFt6b29tLCBzZXRab29tXSA9IHVzZVN0YXRlKDEpXG4gIGNvbnN0IFtzaG93TGFiZWxzLCBzZXRTaG93TGFiZWxzXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtoaXN0b3J5LCBzZXRIaXN0b3J5XSA9IHVzZVN0YXRlPEFubm90YXRpb25TaGFwZVtdW10+KFtdKVxuICBjb25zdCBbaGlzdG9yeUluZGV4LCBzZXRIaXN0b3J5SW5kZXhdID0gdXNlU3RhdGUoLTEpXG4gIGNvbnN0IFtzZWxlY3RlZEltYWdlLCBzZXRTZWxlY3RlZEltYWdlXSA9IHVzZVN0YXRlPEltYWdlRGF0YSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtpbWFnZUxpc3QsIHNldEltYWdlTGlzdF0gPSB1c2VTdGF0ZTxJbWFnZURhdGFbXT4oW10pXG4gIGNvbnN0IFtjdXJyZW50SW1hZ2VJbmRleCwgc2V0Q3VycmVudEltYWdlSW5kZXhdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgW2RhdGFzZXRJZCwgc2V0RGF0YXNldElkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzaG93SW1hZ2VTZWxlY3Rvciwgc2V0U2hvd0ltYWdlU2VsZWN0b3JdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcblxuICAvLyBMb2FkIGltYWdlIGZyb20gVVJMIHBhcmFtcyBvciBzaG93IHNlbGVjdG9yXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2ltYWdlSWQnKVxuICAgIGNvbnN0IHVybERhdGFzZXRJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2RhdGFzZXRJZCcpXG5cbiAgICBpZiAodXJsRGF0YXNldElkKSB7XG4gICAgICBzZXREYXRhc2V0SWQodXJsRGF0YXNldElkKVxuICAgICAgbG9hZERhdGFzZXRJbWFnZXModXJsRGF0YXNldElkKVxuICAgIH0gZWxzZSBpZiAoaW1hZ2VJZCkge1xuICAgICAgbG9hZEltYWdlQnlJZChpbWFnZUlkKVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBMb2FkIGEgZGVmYXVsdCBpbWFnZSBmb3IgaW1tZWRpYXRlIHVzZVxuICAgICAgbG9hZERlZmF1bHRJbWFnZSgpXG4gICAgfVxuICB9LCBbc2VhcmNoUGFyYW1zXSlcblxuICBjb25zdCBsb2FkRGVmYXVsdEltYWdlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgZGVmYXVsdCBpbWFnZS4uLicpXG4gICAgICAvLyBMb2FkIHRoZSBmaXJzdCBhdmFpbGFibGUgaW1hZ2UgYXMgZGVmYXVsdFxuICAgICAgY29uc3QgaW1hZ2UgPSBhd2FpdCBpbWFnZVNlcnZpY2UuZ2V0UmFuZG9tSW1hZ2UoKVxuICAgICAgY29uc29sZS5sb2coJ0RlZmF1bHQgaW1hZ2UgbG9hZGVkOicsIGltYWdlKVxuICAgICAgaWYgKGltYWdlKSB7XG4gICAgICAgIHNldFNlbGVjdGVkSW1hZ2UoaW1hZ2UpXG4gICAgICAgIHNldFNob3dJbWFnZVNlbGVjdG9yKGZhbHNlKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ05vIGltYWdlcyBhdmFpbGFibGUsIHNob3dpbmcgc2VsZWN0b3InKVxuICAgICAgICAvLyBJZiBubyBpbWFnZXMgYXZhaWxhYmxlLCBzaG93IHNlbGVjdG9yXG4gICAgICAgIHNldFNob3dJbWFnZVNlbGVjdG9yKHRydWUpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGRlZmF1bHQgaW1hZ2U6JywgZXJyb3IpXG4gICAgICBzZXRTaG93SW1hZ2VTZWxlY3Rvcih0cnVlKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGxvYWRJbWFnZUJ5SWQgPSBhc3luYyAoaW1hZ2VJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBpbWFnZSA9IGF3YWl0IGltYWdlU2VydmljZS5nZXRJbWFnZShpbWFnZUlkKVxuICAgICAgaWYgKGltYWdlKSB7XG4gICAgICAgIHNldFNlbGVjdGVkSW1hZ2UoaW1hZ2UpXG4gICAgICAgIHNldFNob3dJbWFnZVNlbGVjdG9yKGZhbHNlKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBpbWFnZTonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBsb2FkRGF0YXNldEltYWdlcyA9IGFzeW5jIChkYXRhc2V0SWQ6IHN0cmluZykgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgaW1hZ2VzIGZyb20gZGF0YXNldDonLCBkYXRhc2V0SWQpXG4gICAgICBjb25zdCBpbWFnZXMgPSBhd2FpdCBpbWFnZVNlcnZpY2UuZ2V0SW1hZ2VzQnlEYXRhc2V0KGRhdGFzZXRJZClcbiAgICAgIGNvbnNvbGUubG9nKCdMb2FkZWQgaW1hZ2VzOicsIGltYWdlcylcblxuICAgICAgaWYgKGltYWdlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHNldEltYWdlTGlzdChpbWFnZXMpXG4gICAgICAgIHNldEN1cnJlbnRJbWFnZUluZGV4KDApXG4gICAgICAgIHNldFNlbGVjdGVkSW1hZ2UoaW1hZ2VzWzBdKVxuICAgICAgICBzZXRTaG93SW1hZ2VTZWxlY3RvcihmYWxzZSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdObyBpbWFnZXMgZm91bmQgaW4gZGF0YXNldCwgc2hvd2luZyBzZWxlY3RvcicpXG4gICAgICAgIHNldFNob3dJbWFnZVNlbGVjdG9yKHRydWUpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGRhdGFzZXQgaW1hZ2VzOicsIGVycm9yKVxuICAgICAgc2V0U2hvd0ltYWdlU2VsZWN0b3IodHJ1ZSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBsb2FkUmFuZG9tSW1hZ2VGcm9tRGF0YXNldCA9IGFzeW5jIChkYXRhc2V0SWQ6IHN0cmluZykgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgY29uc3QgaW1hZ2UgPSBhd2FpdCBpbWFnZVNlcnZpY2UuZ2V0UmFuZG9tSW1hZ2UoZGF0YXNldElkKVxuICAgICAgaWYgKGltYWdlKSB7XG4gICAgICAgIHNldFNlbGVjdGVkSW1hZ2UoaW1hZ2UpXG4gICAgICAgIHNldFNob3dJbWFnZVNlbGVjdG9yKGZhbHNlKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCByYW5kb20gaW1hZ2U6JywgZXJyb3IpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cblxuXG4gIC8vIFByZWRlZmluZWQgbGFiZWxzIHdpdGggY29sb3JzXG4gIGNvbnN0IHByZWRlZmluZWRMYWJlbHMgPSBbXG4gICAgeyBuYW1lOiAn5Lq654mpJywgY29sb3I6ICcjZWY0NDQ0JyB9LFxuICAgIHsgbmFtZTogJ+i9pui+hicsIGNvbG9yOiAnIzNiODJmNicgfSxcbiAgICB7IG5hbWU6ICflu7rnrZEnLCBjb2xvcjogJyMxMGI5ODEnIH0sXG4gICAgeyBuYW1lOiAn5Yqo54mpJywgY29sb3I6ICcjZjU5ZTBiJyB9LFxuICAgIHsgbmFtZTogJ+akjeeJqScsIGNvbG9yOiAnIzhiNWNmNicgfSxcbiAgICB7IG5hbWU6ICfniankvZMnLCBjb2xvcjogJyNlYzQ4OTknIH0sXG4gIF1cblxuICBjb25zdCBnZXRDdXJyZW50Q29sb3IgPSAoKSA9PiB7XG4gICAgY29uc3QgbGFiZWxDb25maWcgPSBwcmVkZWZpbmVkTGFiZWxzLmZpbmQobCA9PiBsLm5hbWUgPT09IGN1cnJlbnRMYWJlbClcbiAgICByZXR1cm4gbGFiZWxDb25maWc/LmNvbG9yIHx8ICcjZWY0NDQ0J1xuICB9XG5cbiAgLy8gRXZlbnQgaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlQW5ub3RhdGlvbkNyZWF0ZSA9IChhbm5vdGF0aW9uOiBBbm5vdGF0aW9uU2hhcGUpID0+IHtcbiAgICBzZXRBbm5vdGF0aW9ucyhwcmV2ID0+IFsuLi5wcmV2LCBhbm5vdGF0aW9uXSlcbiAgICBzYXZlVG9IaXN0b3J5KFsuLi5hbm5vdGF0aW9ucywgYW5ub3RhdGlvbl0pXG4gIH1cblxuICBjb25zdCBoYW5kbGVBbm5vdGF0aW9uVXBkYXRlID0gKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8QW5ub3RhdGlvblNoYXBlPikgPT4ge1xuICAgIHNldEFubm90YXRpb25zKHByZXYgPT4gcHJldi5tYXAoYW5uID0+XG4gICAgICBhbm4uaWQgPT09IGlkID8geyAuLi5hbm4sIC4uLnVwZGF0ZXMgfSA6IGFublxuICAgICkpXG4gIH1cblxuICBjb25zdCBoYW5kbGVBbm5vdGF0aW9uRGVsZXRlID0gKGlkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRBbm5vdGF0aW9ucyhwcmV2ID0+IHByZXYuZmlsdGVyKGFubiA9PiBhbm4uaWQgIT09IGlkKSlcbiAgICBzZXRTZWxlY3RlZEFubm90YXRpb24obnVsbClcbiAgICBzYXZlVG9IaXN0b3J5KGFubm90YXRpb25zLmZpbHRlcihhbm4gPT4gYW5uLmlkICE9PSBpZCkpXG4gIH1cblxuICBjb25zdCBoYW5kbGVBbm5vdGF0aW9uVmlzaWJpbGl0eVRvZ2dsZSA9IChpZDogc3RyaW5nKSA9PiB7XG4gICAgLy8gRm9yIG5vdywganVzdCBzZWxlY3QvZGVzZWxlY3QgdGhlIGFubm90YXRpb25cbiAgICBzZXRTZWxlY3RlZEFubm90YXRpb24oc2VsZWN0ZWRBbm5vdGF0aW9uID09PSBpZCA/IG51bGwgOiBpZClcbiAgfVxuXG4gIGNvbnN0IHNhdmVUb0hpc3RvcnkgPSAobmV3QW5ub3RhdGlvbnM/OiBBbm5vdGF0aW9uU2hhcGVbXSkgPT4ge1xuICAgIGNvbnN0IGFubm90YXRpb25zVG9TYXZlID0gbmV3QW5ub3RhdGlvbnMgfHwgYW5ub3RhdGlvbnNcbiAgICBjb25zdCBuZXdIaXN0b3J5ID0gaGlzdG9yeS5zbGljZSgwLCBoaXN0b3J5SW5kZXggKyAxKVxuICAgIG5ld0hpc3RvcnkucHVzaChbLi4uYW5ub3RhdGlvbnNUb1NhdmVdKVxuICAgIHNldEhpc3RvcnkobmV3SGlzdG9yeSlcbiAgICBzZXRIaXN0b3J5SW5kZXgobmV3SGlzdG9yeS5sZW5ndGggLSAxKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVW5kbyA9ICgpID0+IHtcbiAgICBpZiAoaGlzdG9yeUluZGV4ID4gMCkge1xuICAgICAgc2V0SGlzdG9yeUluZGV4KGhpc3RvcnlJbmRleCAtIDEpXG4gICAgICBzZXRBbm5vdGF0aW9ucyhbLi4uaGlzdG9yeVtoaXN0b3J5SW5kZXggLSAxXV0pXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlUmVkbyA9ICgpID0+IHtcbiAgICBpZiAoaGlzdG9yeUluZGV4IDwgaGlzdG9yeS5sZW5ndGggLSAxKSB7XG4gICAgICBzZXRIaXN0b3J5SW5kZXgoaGlzdG9yeUluZGV4ICsgMSlcbiAgICAgIHNldEFubm90YXRpb25zKFsuLi5oaXN0b3J5W2hpc3RvcnlJbmRleCArIDFdXSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVDbGVhciA9ICgpID0+IHtcbiAgICBpZiAod2luZG93LmNvbmZpcm0oJ+ehruWumuimgea4heepuuaJgOacieagh+azqOWQl++8nycpKSB7XG4gICAgICBzZXRBbm5vdGF0aW9ucyhbXSlcbiAgICAgIHNldFNlbGVjdGVkQW5ub3RhdGlvbihudWxsKVxuICAgICAgc2F2ZVRvSGlzdG9yeShbXSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVTYXZlID0gKCkgPT4ge1xuICAgIC8vIFNhdmUgYW5ub3RhdGlvbnMgbG9naWMgaGVyZVxuICAgIGNvbnNvbGUubG9nKCdTYXZpbmcgYW5ub3RhdGlvbnM6JywgYW5ub3RhdGlvbnMpXG4gICAgYWxlcnQoYOW3suS/neWtmCAke2Fubm90YXRpb25zLmxlbmd0aH0g5Liq5qCH5rOoYClcbiAgICByb3V0ZXIucHVzaChST1VURVMuQU5OT1RBVElPTlMpXG4gIH1cblxuICAvLyBOYXZpZ2F0aW9uIGZ1bmN0aW9uc1xuICBjb25zdCBnb1RvUHJldmlvdXNJbWFnZSA9ICgpID0+IHtcbiAgICBpZiAoaW1hZ2VMaXN0Lmxlbmd0aCA+IDAgJiYgY3VycmVudEltYWdlSW5kZXggPiAwKSB7XG4gICAgICBjb25zdCBuZXdJbmRleCA9IGN1cnJlbnRJbWFnZUluZGV4IC0gMVxuICAgICAgc2V0Q3VycmVudEltYWdlSW5kZXgobmV3SW5kZXgpXG4gICAgICBzZXRTZWxlY3RlZEltYWdlKGltYWdlTGlzdFtuZXdJbmRleF0pXG4gICAgICAvLyBDbGVhciBhbm5vdGF0aW9ucyB3aGVuIHN3aXRjaGluZyBpbWFnZXNcbiAgICAgIHNldEFubm90YXRpb25zKFtdKVxuICAgICAgc2V0U2VsZWN0ZWRBbm5vdGF0aW9uKG51bGwpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ29Ub05leHRJbWFnZSA9ICgpID0+IHtcbiAgICBpZiAoaW1hZ2VMaXN0Lmxlbmd0aCA+IDAgJiYgY3VycmVudEltYWdlSW5kZXggPCBpbWFnZUxpc3QubGVuZ3RoIC0gMSkge1xuICAgICAgY29uc3QgbmV3SW5kZXggPSBjdXJyZW50SW1hZ2VJbmRleCArIDFcbiAgICAgIHNldEN1cnJlbnRJbWFnZUluZGV4KG5ld0luZGV4KVxuICAgICAgc2V0U2VsZWN0ZWRJbWFnZShpbWFnZUxpc3RbbmV3SW5kZXhdKVxuICAgICAgLy8gQ2xlYXIgYW5ub3RhdGlvbnMgd2hlbiBzd2l0Y2hpbmcgaW1hZ2VzXG4gICAgICBzZXRBbm5vdGF0aW9ucyhbXSlcbiAgICAgIHNldFNlbGVjdGVkQW5ub3RhdGlvbihudWxsKVxuICAgIH1cbiAgfVxuXG4gIC8vIFNob3cgbG9hZGluZyBzdGF0ZVxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPuato+WcqOWKoOi9veWbvuWDjy4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBTaG93IGltYWdlIHNlbGVjdG9yIGlmIG5vIGltYWdlIGlzIHNlbGVjdGVkXG4gIGlmIChzaG93SW1hZ2VTZWxlY3RvciB8fCAhc2VsZWN0ZWRJbWFnZSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTEwIHctMTAgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj7lm77lg4/moIfms6g8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTJcIj7pgInmi6nopoHmoIfms6jnmoTlm77lg488L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFNpbXBsZSBJbWFnZSBTZWxlY3RvciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGU+6YCJ5oup5Zu+5YOP6L+b6KGM5qCH5rOoPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+54K55Ye75LiL5pa55Zu+5YOP5byA5aeL5qCH5rOoPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnaW1nXzAwMScsIG5hbWU6ICfnjKvlkqrlm77niYcnLCB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUxNDg4ODI4Njk3NC02YzAzZTJjYTFkYmE/dz00MDAmaD0zMDAmZml0PWNyb3AnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnaW1nXzAwMicsIG5hbWU6ICfooZfmma/lm77niYcnLCB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ0OTgyNDkxMzkzNS01OWExMGI4ZDIwMDA/dz00MDAmaD0zMDAmZml0PWNyb3AnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnaW1nXzAwMycsIG5hbWU6ICfni5fni5flm77niYcnLCB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU1MjA1MzgzMS03MTU5NGEyNzYzMmQ/dz00MDAmaD0zMDAmZml0PWNyb3AnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnaW1nXzAwNCcsIG5hbWU6ICflu7rnrZHlm77niYcnLCB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ4NjQwNjE0NjkyNi1jNjI3YTkyYWQxYWI/dz00MDAmaD0zMDAmZml0PWNyb3AnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnaW1nXzAwNScsIG5hbWU6ICfpo47mma/lm77niYcnLCB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNjkwNTkyNTM0Ni0yMWJkYTRkMzJkZjQ/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1cbiAgICAgICAgICAgICAgICBdLm1hcCgoaW1nKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17aW1nLmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciByb3VuZGVkLWxnIGJvcmRlci0yIGJvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItYmx1ZS01MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRJbWFnZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZDogaW1nLmlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogaW1nLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICB1cmw6IGltZy51cmwsXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogODAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiA2MDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplOiAyMDAwMDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBmb3JtYXQ6ICdqcGcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YXNldElkOiAnMScsXG4gICAgICAgICAgICAgICAgICAgICAgICB1cGxvYWRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgdGFnczogW11cbiAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dJbWFnZVNlbGVjdG9yKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFzcGVjdC12aWRlbyByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC10LWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtpbWcudXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXtpbWcubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj57aW1nLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtMTAgdy0xMCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPuWbvuWDj+agh+azqDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTJcIj5cbiAgICAgICAgICAgICAge3NlbGVjdGVkSW1hZ2UubmFtZX1cbiAgICAgICAgICAgICAge2ltYWdlTGlzdC5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICh7Y3VycmVudEltYWdlSW5kZXggKyAxfSAvIHtpbWFnZUxpc3QubGVuZ3RofSlcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgIHsvKiBJbWFnZSBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIHtpbWFnZUxpc3QubGVuZ3RoID4gMSAmJiAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtOSB3LTkgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9QcmV2aW91c0ltYWdlfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50SW1hZ2VJbmRleCA9PT0gMH1cbiAgICAgICAgICAgICAgICB0aXRsZT1cIuS4iuS4gOW8oOWbvueJh1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtOSB3LTkgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9OZXh0SW1hZ2V9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRJbWFnZUluZGV4ID09PSBpbWFnZUxpc3QubGVuZ3RoIC0gMX1cbiAgICAgICAgICAgICAgICB0aXRsZT1cIuS4i+S4gOW8oOWbvueJh1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvPlxuICAgICAgICAgICl9XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtOSByb3VuZGVkLW1kIHB4LTMgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIHRleHQtc21cIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0ltYWdlU2VsZWN0b3IodHJ1ZSl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEltYWdlSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAg5YiH5o2i5Zu+5YOPXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtNSBnYXAtNlwiPlxuICAgICAgICB7LyogTGVmdCBTaWRlYmFyIC0gVG9vbHMgYW5kIEFubm90YXRpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgIDxBbm5vdGF0aW9uVG9vbGJhclxuICAgICAgICAgICAgY3VycmVudFRvb2w9e2N1cnJlbnRUb29sfVxuICAgICAgICAgICAgb25Ub29sQ2hhbmdlPXtzZXRDdXJyZW50VG9vbH1cbiAgICAgICAgICAgIGN1cnJlbnRMYWJlbD17Y3VycmVudExhYmVsfVxuICAgICAgICAgICAgb25MYWJlbENoYW5nZT17c2V0Q3VycmVudExhYmVsfVxuICAgICAgICAgICAgcHJlZGVmaW5lZExhYmVscz17cHJlZGVmaW5lZExhYmVsc31cbiAgICAgICAgICAgIHpvb209e3pvb219XG4gICAgICAgICAgICBvblpvb21DaGFuZ2U9e3NldFpvb219XG4gICAgICAgICAgICBzaG93TGFiZWxzPXtzaG93TGFiZWxzfVxuICAgICAgICAgICAgb25TaG93TGFiZWxzVG9nZ2xlPXsoKSA9PiBzZXRTaG93TGFiZWxzKCFzaG93TGFiZWxzKX1cbiAgICAgICAgICAgIGNhblVuZG89e2hpc3RvcnlJbmRleCA+IDB9XG4gICAgICAgICAgICBjYW5SZWRvPXtoaXN0b3J5SW5kZXggPCBoaXN0b3J5Lmxlbmd0aCAtIDF9XG4gICAgICAgICAgICBvblVuZG89e2hhbmRsZVVuZG99XG4gICAgICAgICAgICBvblJlZG89e2hhbmRsZVJlZG99XG4gICAgICAgICAgICBvblNhdmU9e2hhbmRsZVNhdmV9XG4gICAgICAgICAgICBvbkNsZWFyPXtoYW5kbGVDbGVhcn1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPEFubm90YXRpb25MaXN0XG4gICAgICAgICAgICBhbm5vdGF0aW9ucz17YW5ub3RhdGlvbnN9XG4gICAgICAgICAgICBzZWxlY3RlZEFubm90YXRpb249e3NlbGVjdGVkQW5ub3RhdGlvbn1cbiAgICAgICAgICAgIG9uQW5ub3RhdGlvblNlbGVjdD17c2V0U2VsZWN0ZWRBbm5vdGF0aW9ufVxuICAgICAgICAgICAgb25Bbm5vdGF0aW9uRGVsZXRlPXtoYW5kbGVBbm5vdGF0aW9uRGVsZXRlfVxuICAgICAgICAgICAgb25Bbm5vdGF0aW9uVXBkYXRlPXtoYW5kbGVBbm5vdGF0aW9uVXBkYXRlfVxuICAgICAgICAgICAgb25Bbm5vdGF0aW9uVmlzaWJpbGl0eVRvZ2dsZT17aGFuZGxlQW5ub3RhdGlvblZpc2liaWxpdHlUb2dnbGV9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1haW4gQ2FudmFzIEFyZWEgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tM1wiPlxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGU+5Zu+5YOP5qCH5rOo5Yy65Z+fPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAg5L2/55So5bem5L6n5bel5YW35Zyo5Zu+5YOP5LiK5Yib5bu65qCH5rOoIC0g5b2T5YmN5bel5YW3OiB7Y3VycmVudFRvb2wgPT09ICdzZWxlY3QnID8gJ+mAieaLqScgOiBjdXJyZW50VG9vbCA9PT0gJ3JlY3RhbmdsZScgPyAn55+p5b2iJyA6IGN1cnJlbnRUb29sID09PSAnY2lyY2xlJyA/ICflnIblvaInIDogY3VycmVudFRvb2wgPT09ICdwb2ludCcgPyAn54K55qCH5rOoJyA6ICflpJrovrnlvaInfVxuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIG92ZXJmbG93LWF1dG8gYm9yZGVyIHJvdW5kZWQtbGcgYmctZ3JheS01MFwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgbWF4SGVpZ2h0OiAnNjAwcHgnIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRyYW5zZm9ybTogYHNjYWxlKCR7em9vbX0pYCwgdHJhbnNmb3JtT3JpZ2luOiAndG9wIGxlZnQnIH19PlxuICAgICAgICAgICAgICAgICAgPEltYWdlQW5ub3RhdGlvbkNhbnZhc1xuICAgICAgICAgICAgICAgICAgICBpbWFnZVVybD17c2VsZWN0ZWRJbWFnZS51cmx9XG4gICAgICAgICAgICAgICAgICAgIGltYWdlV2lkdGg9e3NlbGVjdGVkSW1hZ2Uud2lkdGh9XG4gICAgICAgICAgICAgICAgICAgIGltYWdlSGVpZ2h0PXtzZWxlY3RlZEltYWdlLmhlaWdodH1cbiAgICAgICAgICAgICAgICAgICAgYW5ub3RhdGlvbnM9e2Fubm90YXRpb25zfVxuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZEFubm90YXRpb249e3NlbGVjdGVkQW5ub3RhdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFRvb2w9e2N1cnJlbnRUb29sfVxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50TGFiZWw9e2N1cnJlbnRMYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgY3VycmVudENvbG9yPXtnZXRDdXJyZW50Q29sb3IoKX1cbiAgICAgICAgICAgICAgICAgICAgem9vbT17em9vbX1cbiAgICAgICAgICAgICAgICAgICAgc2hvd0xhYmVscz17c2hvd0xhYmVsc31cbiAgICAgICAgICAgICAgICAgICAgb25Bbm5vdGF0aW9uQ3JlYXRlPXtoYW5kbGVBbm5vdGF0aW9uQ3JlYXRlfVxuICAgICAgICAgICAgICAgICAgICBvbkFubm90YXRpb25VcGRhdGU9e2hhbmRsZUFubm90YXRpb25VcGRhdGV9XG4gICAgICAgICAgICAgICAgICAgIG9uQW5ub3RhdGlvblNlbGVjdD17c2V0U2VsZWN0ZWRBbm5vdGF0aW9ufVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHAtMyBiZy1ibHVlLTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtOTAwIG1iLTJcIj7kvb/nlKjor7TmmI46PC9oND5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTgwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgIDxwPuKAoiDpgInmi6nlt6XlhbflkozmoIfnrb7lkI7vvIzlnKjlm77lg4/kuIrmi5bmi73liJvlu7rmoIfms6g8L3A+XG4gICAgICAgICAgICAgICAgICA8cD7igKIg54K55Ye75qCH5rOo5Y+v5Lul6YCJ5Lit77yM5Zyo5Y+z5L6n5YiX6KGo5Lit57yW6L6R5qCH562+PC9wPlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIOS9v+eUqOe8qeaUvuW3peWFt+WPr+S7peaUvuWkp+afpeeci+e7huiKgjwvcD5cbiAgICAgICAgICAgICAgICAgIDxwPuKAoiDmlK/mjIHmkqTplIAv6YeN5YGa5pON5L2c77yM5Y+v5Lul6ZqP5pe25oGi5aSN5LmL5YmN55qE54q25oCBPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogU3RhdGlzdGljcyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57YW5ub3RhdGlvbnMubGVuZ3RofTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7mgLvmoIfms6jmlbA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57YW5ub3RhdGlvbnMuZmlsdGVyKGEgPT4gYS5sYWJlbCkubGVuZ3RofTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7lt7LmoIforrA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57TWF0aC5yb3VuZCh6b29tICogMTAwKX0lPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPue8qeaUvuavlOS+izwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntoaXN0b3J5Lmxlbmd0aH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+5Y6G5Y+y6K6w5b2VPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBEZWJ1ZyBJbmZvICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcC0zIGJnLXllbGxvdy01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXIteWVsbG93LTIwMFwiPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQteWVsbG93LTkwMCBtYi0yXCI+6LCD6K+V5L+h5oGvOjwvaDQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQteWVsbG93LTgwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgIDxwPuKAoiDpgInkuK3lm77lg486IHtzZWxlY3RlZEltYWdlID8gc2VsZWN0ZWRJbWFnZS5uYW1lIDogJ+aXoCd9PC9wPlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIOWbvuWDj1VSTDoge3NlbGVjdGVkSW1hZ2UgPyBzZWxlY3RlZEltYWdlLnVybCA6ICfml6AnfTwvcD5cbiAgICAgICAgICAgICAgICAgIDxwPuKAoiDmmL7npLrpgInmi6nlmag6IHtzaG93SW1hZ2VTZWxlY3RvciA/ICfmmK8nIDogJ+WQpid9PC9wPlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIOWKoOi9veeKtuaAgToge2xvYWRpbmcgPyAn5Yqg6L295LitJyA6ICflt7LlrozmiJAnfTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlU2VhcmNoUGFyYW1zIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlJPVVRFUyIsIkFycm93TGVmdCIsIkltYWdlIiwiSW1hZ2VJY29uIiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uUmlnaHQiLCJJbWFnZUFubm90YXRpb25DYW52YXMiLCJBbm5vdGF0aW9uVG9vbGJhciIsIkFubm90YXRpb25MaXN0IiwiaW1hZ2VTZXJ2aWNlIiwiSW1hZ2VBbm5vdGF0ZVBhZ2UiLCJjdXJyZW50VG9vbCIsInNldEN1cnJlbnRUb29sIiwiYW5ub3RhdGlvbnMiLCJzZXRBbm5vdGF0aW9ucyIsInNlbGVjdGVkQW5ub3RhdGlvbiIsInNldFNlbGVjdGVkQW5ub3RhdGlvbiIsImN1cnJlbnRMYWJlbCIsInNldEN1cnJlbnRMYWJlbCIsInpvb20iLCJzZXRab29tIiwic2hvd0xhYmVscyIsInNldFNob3dMYWJlbHMiLCJoaXN0b3J5Iiwic2V0SGlzdG9yeSIsImhpc3RvcnlJbmRleCIsInNldEhpc3RvcnlJbmRleCIsInNlbGVjdGVkSW1hZ2UiLCJzZXRTZWxlY3RlZEltYWdlIiwiaW1hZ2VMaXN0Iiwic2V0SW1hZ2VMaXN0IiwiY3VycmVudEltYWdlSW5kZXgiLCJzZXRDdXJyZW50SW1hZ2VJbmRleCIsImRhdGFzZXRJZCIsInNldERhdGFzZXRJZCIsInNob3dJbWFnZVNlbGVjdG9yIiwic2V0U2hvd0ltYWdlU2VsZWN0b3IiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJvdXRlciIsInNlYXJjaFBhcmFtcyIsImltYWdlSWQiLCJnZXQiLCJ1cmxEYXRhc2V0SWQiLCJsb2FkRGF0YXNldEltYWdlcyIsImxvYWRJbWFnZUJ5SWQiLCJsb2FkRGVmYXVsdEltYWdlIiwiY29uc29sZSIsImxvZyIsImltYWdlIiwiZ2V0UmFuZG9tSW1hZ2UiLCJlcnJvciIsImdldEltYWdlIiwiaW1hZ2VzIiwiZ2V0SW1hZ2VzQnlEYXRhc2V0IiwibGVuZ3RoIiwibG9hZFJhbmRvbUltYWdlRnJvbURhdGFzZXQiLCJwcmVkZWZpbmVkTGFiZWxzIiwibmFtZSIsImNvbG9yIiwiZ2V0Q3VycmVudENvbG9yIiwibGFiZWxDb25maWciLCJmaW5kIiwibCIsImhhbmRsZUFubm90YXRpb25DcmVhdGUiLCJhbm5vdGF0aW9uIiwicHJldiIsInNhdmVUb0hpc3RvcnkiLCJoYW5kbGVBbm5vdGF0aW9uVXBkYXRlIiwiaWQiLCJ1cGRhdGVzIiwibWFwIiwiYW5uIiwiaGFuZGxlQW5ub3RhdGlvbkRlbGV0ZSIsImZpbHRlciIsImhhbmRsZUFubm90YXRpb25WaXNpYmlsaXR5VG9nZ2xlIiwibmV3QW5ub3RhdGlvbnMiLCJhbm5vdGF0aW9uc1RvU2F2ZSIsIm5ld0hpc3RvcnkiLCJzbGljZSIsInB1c2giLCJoYW5kbGVVbmRvIiwiaGFuZGxlUmVkbyIsImhhbmRsZUNsZWFyIiwid2luZG93IiwiY29uZmlybSIsImhhbmRsZVNhdmUiLCJhbGVydCIsIkFOTk9UQVRJT05TIiwiZ29Ub1ByZXZpb3VzSW1hZ2UiLCJuZXdJbmRleCIsImdvVG9OZXh0SW1hZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiYnV0dG9uIiwib25DbGljayIsImJhY2siLCJoMSIsInVybCIsImltZyIsIndpZHRoIiwiaGVpZ2h0Iiwic2l6ZSIsImZvcm1hdCIsInVwbG9hZGVkQXQiLCJEYXRlIiwidGFncyIsInNyYyIsImFsdCIsInNwYW4iLCJkaXNhYmxlZCIsInRpdGxlIiwib25Ub29sQ2hhbmdlIiwib25MYWJlbENoYW5nZSIsIm9uWm9vbUNoYW5nZSIsIm9uU2hvd0xhYmVsc1RvZ2dsZSIsImNhblVuZG8iLCJjYW5SZWRvIiwib25VbmRvIiwib25SZWRvIiwib25TYXZlIiwib25DbGVhciIsIm9uQW5ub3RhdGlvblNlbGVjdCIsIm9uQW5ub3RhdGlvbkRlbGV0ZSIsIm9uQW5ub3RhdGlvblVwZGF0ZSIsIm9uQW5ub3RhdGlvblZpc2liaWxpdHlUb2dnbGUiLCJzdHlsZSIsIm1heEhlaWdodCIsInRyYW5zZm9ybSIsInRyYW5zZm9ybU9yaWdpbiIsImltYWdlVXJsIiwiaW1hZ2VXaWR0aCIsImltYWdlSGVpZ2h0IiwiY3VycmVudENvbG9yIiwib25Bbm5vdGF0aW9uQ3JlYXRlIiwiaDQiLCJhIiwibGFiZWwiLCJNYXRoIiwicm91bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/annotations/image-annotate/page.tsx\n"));

/***/ })

});
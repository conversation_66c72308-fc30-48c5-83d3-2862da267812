'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, ImageIcon } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function WorkingImageAnnotatePage() {
  const router = useRouter()
  const [selectedImage, setSelectedImage] = useState({
    id: 'img_001',
    name: '猫咪图片',
    url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=800&h=600&fit=crop',
    width: 800,
    height: 600
  })

  const images = [
    { id: 'img_001', name: '猫咪图片', url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=800&h=600&fit=crop' },
    { id: 'img_002', name: '街景图片', url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop' },
    { id: 'img_003', name: '狗狗图片', url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=800&h=600&fit=crop' },
    { id: 'img_004', name: '建筑图片', url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop' },
    { id: 'img_005', name: '风景图片', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            className="inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">图像标注 - 工作版本</h1>
            <p className="text-gray-600 mt-2">确保图像能正确显示的版本</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar - Image Selection */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>选择图像</CardTitle>
              <CardDescription>点击图像进行标注</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {images.map((img) => (
                  <div
                    key={img.id}
                    className={`cursor-pointer rounded-lg border-2 p-2 transition-colors ${
                      selectedImage.id === img.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                    onClick={() => setSelectedImage({
                      ...img,
                      width: 800,
                      height: 600
                    })}
                  >
                    <div className="aspect-video relative overflow-hidden rounded">
                      <img
                        src={img.url}
                        alt={img.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="mt-2 text-sm font-medium">{img.name}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Simple Tools */}
          <Card>
            <CardHeader>
              <CardTitle>标注工具</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <button className="w-full px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                  矩形标注
                </button>
                <button className="w-full px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                  圆形标注
                </button>
                <button className="w-full px-3 py-2 text-sm bg-purple-600 text-white rounded hover:bg-purple-700">
                  点标注
                </button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Image Area */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>图像标注区域</CardTitle>
              <CardDescription>
                当前图像: {selectedImage.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg overflow-hidden bg-gray-50">
                <div className="relative">
                  <img
                    src={selectedImage.url}
                    alt={selectedImage.name}
                    className="w-full h-auto max-h-[600px] object-contain"
                    style={{ display: 'block' }}
                  />
                  
                  {/* Overlay for annotations */}
                  <div className="absolute inset-0 pointer-events-none">
                    {/* This is where annotations would be rendered */}
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                <h4 className="text-sm font-medium text-green-900 mb-2">✅ 图像显示状态:</h4>
                <div className="text-sm text-green-800 space-y-1">
                  <p>• 图像URL: {selectedImage.url}</p>
                  <p>• 图像尺寸: {selectedImage.width} x {selectedImage.height}</p>
                  <p>• 状态: 图像应该正常显示</p>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明:</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• 左侧选择要标注的图像</p>
                  <p>• 图像会在右侧显示</p>
                  <p>• 选择标注工具开始标注</p>
                  <p>• 这个版本确保图像能正确加载和显示</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

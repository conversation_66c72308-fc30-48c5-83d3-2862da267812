"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 371,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 366,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"fbV+AjM4vIIwRHQRKPy9Hvtsk98=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
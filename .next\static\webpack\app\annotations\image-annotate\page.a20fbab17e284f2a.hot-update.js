"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        const coords = getCanvasCoordinates(e);\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseUp\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"cKR0bKpXOT41g3nXy0GIr86jYvE=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
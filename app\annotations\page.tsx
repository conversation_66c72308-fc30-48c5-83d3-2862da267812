'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { Plus, Search, MoreHorizontal, Database, Calendar, FileText, Image } from 'lucide-react'
import { mockDatasets, getProjectById } from '@/services/mockData'

const statusColors = {
  UPLOADING: 'bg-blue-100 text-blue-800',
  PROCESSING: 'bg-yellow-100 text-yellow-800',
  READY: 'bg-green-100 text-green-800',
  ERROR: 'bg-red-100 text-red-800',
}

const statusLabels = {
  UPLOADING: '上传中',
  PROCESSING: '处理中',
  READY: '就绪',
  ERROR: '错误',
}

export default function AnnotationsPage() {
  const [searchTerm, setSearchTerm] = useState('')

  // Filter datasets that are ready for annotation (IMAGE type and READY status)
  const annotationReadyDatasets = mockDatasets.filter(dataset =>
    dataset.type === 'IMAGE' && dataset.status === 'READY'
  )

  const filteredDatasets = annotationReadyDatasets.filter(dataset =>
    dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dataset.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">标注管理</h1>
          <p className="text-gray-600 mt-2">选择数据集开始图像标注</p>
        </div>
        <div className="flex space-x-2">
          <Link href={ROUTES.IMAGE_ANNOTATE}>
            <Button variant="outline">
              <Image className="h-4 w-4 mr-2" />
              直接进入标注
            </Button>
          </Link>
          <Link href={ROUTES.ANNOTATION_CREATE}>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              创建标注
            </Button>
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索数据集..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Datasets Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDatasets.map((dataset) => {
          const project = getProjectById(dataset.projectId)

          return (
            <Card key={dataset.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{dataset.name}</CardTitle>
                    <CardDescription className="mt-2">
                      {dataset.description}
                    </CardDescription>
                  </div>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        statusColors[dataset.status as keyof typeof statusColors]
                      }`}
                    >
                      {statusLabels[dataset.status as keyof typeof statusLabels]}
                    </span>
                    <div className="flex items-center text-sm text-gray-500">
                      <Database className="h-4 w-4 mr-1" />
                      {dataset.size.toLocaleString()} 张图片
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-500">
                      <FileText className="h-4 w-4 mr-2" />
                      项目：{project?.name}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-2" />
                      创建：{dataset.createdAt.toLocaleDateString()}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link href={ROUTES.DATASET_DETAIL(dataset.id)} className="flex-1">
                      <Button variant="outline" className="w-full">
                        查看详情
                      </Button>
                    </Link>
                    <Link href={ROUTES.IMAGE_ANNOTATE_DATASET(dataset.id)}>
                      <Button className="w-full">
                        <Image className="h-4 w-4 mr-2" />
                        开始标记
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredDatasets.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到可用于标注的数据集</p>
          <p className="text-gray-400 text-sm mt-2">只有状态为"就绪"的图像数据集可以进行标注</p>
        </div>
      )}
    </div>
  )
}

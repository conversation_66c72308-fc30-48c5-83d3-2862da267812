"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./app/annotations/image-annotate/page.tsx":
/*!*************************************************!*\
  !*** ./app/annotations/image-annotate/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageAnnotatePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/routes */ \"(app-pages-browser)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronLeft,ChevronRight,Image!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/annotation/image-annotation-canvas */ \"(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\");\n/* harmony import */ var _components_annotation_compact_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/annotation/compact-annotation-toolbar */ \"(app-pages-browser)/./components/annotation/compact-annotation-toolbar.tsx\");\n/* harmony import */ var _components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/annotation/annotation-list */ \"(app-pages-browser)/./components/annotation/annotation-list.tsx\");\n/* harmony import */ var _services_imageService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/imageService */ \"(app-pages-browser)/./services/imageService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ImageAnnotatePage() {\n    _s();\n    const [currentTool, setCurrentTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [annotations, setAnnotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnnotation, setSelectedAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentLabel, setCurrentLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showLabels, setShowLabels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageList, setImageList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [datasetId, setDatasetId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Load image from URL params or show selector\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotatePage.useEffect\": ()=>{\n            const imageId = searchParams.get('imageId');\n            const urlDatasetId = searchParams.get('datasetId');\n            if (urlDatasetId) {\n                setDatasetId(urlDatasetId);\n                loadDatasetImages(urlDatasetId);\n            } else if (imageId) {\n                loadImageById(imageId);\n            } else {\n                // Load a default image for immediate use\n                loadDefaultImage();\n            }\n        }\n    }[\"ImageAnnotatePage.useEffect\"], [\n        searchParams\n    ]);\n    const loadDefaultImage = async ()=>{\n        setLoading(true);\n        try {\n            console.log('Loading default image...');\n            // Load the first available image as default\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage();\n            console.log('Default image loaded:', image);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            } else {\n                console.log('No images available, showing selector');\n                // If no images available, show selector\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load default image:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadImageById = async (imageId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getImage(imageId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDatasetImages = async (datasetId)=>{\n        setLoading(true);\n        try {\n            console.log('Loading images from dataset:', datasetId);\n            const images = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getImagesByDataset(datasetId);\n            console.log('Loaded images:', images);\n            if (images.length > 0) {\n                setImageList(images);\n                setCurrentImageIndex(0);\n                setSelectedImage(images[0]);\n                setShowImageSelector(false);\n            } else {\n                console.log('No images found in dataset, showing selector');\n                setShowImageSelector(true);\n            }\n        } catch (error) {\n            console.error('Failed to load dataset images:', error);\n            setShowImageSelector(true);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRandomImageFromDataset = async (datasetId)=>{\n        setLoading(true);\n        try {\n            const image = await _services_imageService__WEBPACK_IMPORTED_MODULE_8__.imageService.getRandomImage(datasetId);\n            if (image) {\n                setSelectedImage(image);\n                setShowImageSelector(false);\n            }\n        } catch (error) {\n            console.error('Failed to load random image:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Predefined labels with colors\n    const predefinedLabels = [\n        {\n            name: '人物',\n            color: '#ef4444'\n        },\n        {\n            name: '车辆',\n            color: '#3b82f6'\n        },\n        {\n            name: '建筑',\n            color: '#10b981'\n        },\n        {\n            name: '动物',\n            color: '#f59e0b'\n        },\n        {\n            name: '植物',\n            color: '#8b5cf6'\n        },\n        {\n            name: '物体',\n            color: '#ec4899'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    // Event handlers\n    const handleAnnotationCreate = (annotation)=>{\n        setAnnotations((prev)=>[\n                ...prev,\n                annotation\n            ]);\n        saveToHistory([\n            ...annotations,\n            annotation\n        ]);\n    };\n    const handleAnnotationUpdate = (id, updates)=>{\n        setAnnotations((prev)=>prev.map((ann)=>ann.id === id ? {\n                    ...ann,\n                    ...updates\n                } : ann));\n    };\n    const handleAnnotationDelete = (id)=>{\n        setAnnotations((prev)=>prev.filter((ann)=>ann.id !== id));\n        setSelectedAnnotation(null);\n        saveToHistory(annotations.filter((ann)=>ann.id !== id));\n    };\n    const handleAnnotationVisibilityToggle = (id)=>{\n        // For now, just select/deselect the annotation\n        setSelectedAnnotation(selectedAnnotation === id ? null : id);\n    };\n    const saveToHistory = (newAnnotations)=>{\n        const annotationsToSave = newAnnotations || annotations;\n        const newHistory = history.slice(0, historyIndex + 1);\n        newHistory.push([\n            ...annotationsToSave\n        ]);\n        setHistory(newHistory);\n        setHistoryIndex(newHistory.length - 1);\n    };\n    const handleUndo = ()=>{\n        if (historyIndex > 0) {\n            setHistoryIndex(historyIndex - 1);\n            setAnnotations([\n                ...history[historyIndex - 1]\n            ]);\n        }\n    };\n    const handleRedo = ()=>{\n        if (historyIndex < history.length - 1) {\n            setHistoryIndex(historyIndex + 1);\n            setAnnotations([\n                ...history[historyIndex + 1]\n            ]);\n        }\n    };\n    const handleClear = ()=>{\n        if (window.confirm('确定要清空所有标注吗？')) {\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n            saveToHistory([]);\n        }\n    };\n    const handleSave = ()=>{\n        // Save annotations logic here\n        console.log('Saving annotations:', annotations);\n        alert(\"已保存 \".concat(annotations.length, \" 个标注\"));\n        router.push(_constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.ANNOTATIONS);\n    };\n    // Navigation functions\n    const goToPreviousImage = ()=>{\n        if (imageList.length > 0 && currentImageIndex > 0) {\n            const newIndex = currentImageIndex - 1;\n            setCurrentImageIndex(newIndex);\n            setSelectedImage(imageList[newIndex]);\n            // Clear annotations when switching images\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n        }\n    };\n    const goToNextImage = ()=>{\n        if (imageList.length > 0 && currentImageIndex < imageList.length - 1) {\n            const newIndex = currentImageIndex + 1;\n            setCurrentImageIndex(newIndex);\n            setSelectedImage(imageList[newIndex]);\n            // Clear annotations when switching images\n            setAnnotations([]);\n            setSelectedAnnotation(null);\n        }\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载图像...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    // Show image selector if no image is selected\n    if (showImageSelector || !selectedImage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"选择要标注的图像\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"选择图像进行标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"点击下方图像开始标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        {\n                                            id: 'img_001',\n                                            name: '猫咪图片',\n                                            url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_002',\n                                            name: '街景图片',\n                                            url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_003',\n                                            name: '狗狗图片',\n                                            url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_004',\n                                            name: '建筑图片',\n                                            url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'\n                                        },\n                                        {\n                                            id: 'img_005',\n                                            name: '风景图片',\n                                            url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'\n                                        }\n                                    ].map((img)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"cursor-pointer rounded-lg border-2 border-gray-200 hover:border-blue-500 transition-colors\",\n                                            onClick: ()=>{\n                                                setSelectedImage({\n                                                    id: img.id,\n                                                    name: img.name,\n                                                    url: img.url,\n                                                    width: 800,\n                                                    height: 600,\n                                                    size: 200000,\n                                                    format: 'jpg',\n                                                    datasetId: '1',\n                                                    uploadedAt: new Date(),\n                                                    tags: []\n                                                });\n                                                setShowImageSelector(false);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: img.url,\n                                                        alt: img.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: img.name\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, img.id, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"图像标注\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: [\n                                            selectedImage.name,\n                                            imageList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm\",\n                                                children: [\n                                                    \"(\",\n                                                    currentImageIndex + 1,\n                                                    \" / \",\n                                                    imageList.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            imageList.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center justify-center h-9 w-9 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: goToPreviousImage,\n                                        disabled: currentImageIndex === 0,\n                                        title: \"上一张图片\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center justify-center h-9 w-9 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: goToNextImage,\n                                        disabled: currentImageIndex === imageList.length - 1,\n                                        title: \"下一张图片\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center h-9 rounded-md px-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground text-sm\",\n                                onClick: ()=>setShowImageSelector(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"切换图像\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_annotation_list__WEBPACK_IMPORTED_MODULE_7__.AnnotationList, {\n                            annotations: annotations,\n                            selectedAnnotation: selectedAnnotation,\n                            onAnnotationSelect: setSelectedAnnotation,\n                            onAnnotationDelete: handleAnnotationDelete,\n                            onAnnotationUpdate: handleAnnotationUpdate,\n                            onAnnotationVisibilityToggle: handleAnnotationVisibilityToggle\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"图像标注区域\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                \"使用左侧工具在图像上创建标注 - 当前工具: \",\n                                                currentTool === 'select' ? '选择' : currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : currentTool === 'point' ? '点标注' : '多边形'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-auto border rounded-lg bg-gray-50\",\n                                            style: {\n                                                maxHeight: '600px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_compact_annotation_toolbar__WEBPACK_IMPORTED_MODULE_6__.CompactAnnotationToolbar, {\n                                                    currentTool: currentTool,\n                                                    onToolChange: setCurrentTool,\n                                                    currentLabel: currentLabel,\n                                                    onLabelChange: setCurrentLabel,\n                                                    predefinedLabels: predefinedLabels,\n                                                    zoom: zoom,\n                                                    onZoomChange: setZoom,\n                                                    showLabels: showLabels,\n                                                    onShowLabelsToggle: ()=>setShowLabels(!showLabels),\n                                                    canUndo: historyIndex > 0,\n                                                    canRedo: historyIndex < history.length - 1,\n                                                    onUndo: handleUndo,\n                                                    onRedo: handleRedo,\n                                                    onSave: handleSave,\n                                                    onClear: handleClear\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        transform: \"scale(\".concat(zoom, \")\"),\n                                                        transformOrigin: 'top left'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_annotation_image_annotation_canvas__WEBPACK_IMPORTED_MODULE_5__.ImageAnnotationCanvas, {\n                                                        imageUrl: selectedImage.url,\n                                                        imageWidth: selectedImage.width,\n                                                        imageHeight: selectedImage.height,\n                                                        annotations: annotations,\n                                                        selectedAnnotation: selectedAnnotation,\n                                                        currentTool: currentTool,\n                                                        currentLabel: currentLabel,\n                                                        currentColor: getCurrentColor(),\n                                                        zoom: zoom,\n                                                        showLabels: showLabels,\n                                                        onAnnotationCreate: handleAnnotationCreate,\n                                                        onAnnotationUpdate: handleAnnotationUpdate,\n                                                        onAnnotationSelect: setSelectedAnnotation\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"使用说明:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 选择工具和标签后，在图像上拖拽创建标注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 点击标注可以选中，在右侧列表中编辑标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 使用缩放工具可以放大查看细节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 支持撤销/重做操作，可以随时恢复之前的状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"总标注数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: annotations.filter((a)=>a.label).length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"已标记\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                Math.round(zoom * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"缩放比例\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: history.length\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"历史记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-yellow-900 mb-2\",\n                                                    children: \"调试信息:\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-yellow-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 选中图像: \",\n                                                                selectedImage ? selectedImage.name : '无'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 图像URL: \",\n                                                                selectedImage ? selectedImage.url : '无'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 显示选择器: \",\n                                                                showImageSelector ? '是' : '否'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"• 加载状态: \",\n                                                                loading ? '加载中' : '已完成'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"图片列表\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                \"数据集中的所有图片 (\",\n                                                imageList.length,\n                                                \" 张)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                            children: imageList.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 p-2 rounded-lg border cursor-pointer transition-colors \".concat(currentImageIndex === index ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'),\n                                                    onClick: ()=>{\n                                                        setCurrentImageIndex(index);\n                                                        setSelectedImage(image);\n                                                        // Clear annotations when switching images\n                                                        setAnnotations([]);\n                                                        setSelectedAnnotation(null);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: image.url,\n                                                                alt: image.name,\n                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                    children: image.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        image.width,\n                                                                        \" \\xd7 \",\n                                                                        image.height\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: index + 1\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, image.id, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this),\n                                        imageList.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronLeft_ChevronRight_Image_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: \"暂无图片\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\image-annotate\\\\page.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotatePage, \"a2yQA7zQdP8b4h9Tcj7ru6mVVcY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ImageAnnotatePage;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/annotations/image-annotate/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/annotation/compact-annotation-toolbar.tsx":
/*!**************************************************************!*\
  !*** ./components/annotation/compact-annotation-toolbar.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactAnnotationToolbar: () => (/* binding */ CompactAnnotationToolbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Eye,EyeOff,MapPin,MousePointer,Redo,Save,Square,Tag,Trash2,Undo,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ CompactAnnotationToolbar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Tooltip(param) {\n    let { content, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group\",\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50\",\n                children: [\n                    content,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_c = Tooltip;\nfunction CompactAnnotationToolbar(param) {\n    let { currentTool, onToolChange, currentLabel, onLabelChange, predefinedLabels, zoom, onZoomChange, showLabels, onShowLabelsToggle, canUndo, canRedo, onUndo, onRedo, onSave, onClear } = param;\n    _s();\n    const [showLabelPanel, setShowLabelPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const tools = [\n        {\n            id: 'select',\n            icon: _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: '选择工具'\n        },\n        {\n            id: 'rectangle',\n            icon: _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: '矩形标注'\n        },\n        {\n            id: 'circle',\n            icon: _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: '圆形标注'\n        },\n        {\n            id: 'point',\n            icon: _barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: '点标注'\n        }\n    ];\n    const getCurrentColor = ()=>{\n        const labelConfig = predefinedLabels.find((l)=>l.name === currentLabel);\n        return (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || '#ef4444';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute top-4 left-4 z-10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg border p-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 border-r pr-2\",\n                            children: tools.map((tool)=>{\n                                const Icon = tool.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: tool.label,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: currentTool === tool.id ? 'default' : 'ghost',\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: ()=>onToolChange(tool.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, this)\n                                }, tool.id, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 border-r pr-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"标签管理\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: showLabelPanel ? 'default' : 'ghost',\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: ()=>setShowLabelPanel(!showLabelPanel),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                currentLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"当前标签: \".concat(currentLabel),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 rounded border-2 border-gray-300 cursor-pointer\",\n                                        style: {\n                                            backgroundColor: getCurrentColor()\n                                        },\n                                        onClick: ()=>setShowLabelPanel(!showLabelPanel)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 border-r pr-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: showLabels ? '隐藏标签' : '显示标签',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: onShowLabelsToggle,\n                                        children: showLabels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 31\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 61\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"放大\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: ()=>onZoomChange(Math.min(zoom + 0.1, 3)),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"缩小\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: ()=>onZoomChange(Math.max(zoom - 0.1, 0.1)),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"撤销\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: onUndo,\n                                        disabled: !canUndo,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"重做\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: onRedo,\n                                        disabled: !canRedo,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"保存标注\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: onSave,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                                    content: \"清空所有\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8\",\n                                        onClick: onClear,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Eye_EyeOff_MapPin_MousePointer_Redo_Save_Square_Tag_Trash2_Undo_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                showLabelPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 pt-2 border-t\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 w-64\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-gray-700 mb-1\",\n                                        children: \"预定义标签\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: predefinedLabels.map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-2 py-1 text-xs rounded border \".concat(currentLabel === label.name ? 'border-gray-800 bg-gray-100' : 'border-gray-300 hover:border-gray-400'),\n                                                style: {\n                                                    borderLeftColor: label.color,\n                                                    borderLeftWidth: '3px'\n                                                },\n                                                onClick: ()=>onLabelChange(label.name),\n                                                children: label.name\n                                            }, label.name, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-gray-700 mb-1\",\n                                        children: \"自定义标签\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        placeholder: \"输入标签名称\",\n                                        value: currentLabel,\n                                        onChange: (e)=>onLabelChange(e.target.value),\n                                        className: \"h-8 text-xs\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"缩放: \",\n                                            Math.round(zoom * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0.1\",\n                                        max: \"3\",\n                                        step: \"0.1\",\n                                        value: zoom,\n                                        onChange: (e)=>onZoomChange(parseFloat(e.target.value)),\n                                        className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\compact-annotation-toolbar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(CompactAnnotationToolbar, \"pPwQW9RlMYp4Emge//jLyfAf5r4=\");\n_c1 = CompactAnnotationToolbar;\nvar _c, _c1;\n$RefreshReg$(_c, \"Tooltip\");\n$RefreshReg$(_c1, \"CompactAnnotationToolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/compact-annotation-toolbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Circle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ]\n];\nconst Circle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle\", __iconNode);\n //# sourceMappingURL=circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2lyY2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLENBQU0sZUFBdUI7SUFBQztRQUFDLFNBQVU7UUFBQTtZQUFFLENBQUk7WUFBTSxFQUFJLE9BQU07WUFBQSxDQUFHO1lBQU0sQ0FBSztRQUFBLENBQVU7S0FBQztDQUFBO0FBYXpGLGFBQVMsa0VBQWlCLFdBQVUsQ0FBVSIsInNvdXJjZXMiOlsiRjpcXHNyY1xcaWNvbnNcXGNpcmNsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaXJjbGVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOFkybHlZMnhsSUdONFBTSXhNaUlnWTNrOUlqRXlJaUJ5UFNJeE1DSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaXJjbGVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaXJjbGUgPSBjcmVhdGVMdWNpZGVJY29uKCdjaXJjbGUnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2lyY2xlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/eye-off.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ EyeOff)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49\",\n            key: \"ct8e1f\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14.084 14.158a3 3 0 0 1-4.242-4.242\",\n            key: \"151rxh\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143\",\n            key: \"13bj9a\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m2 2 20 20\",\n            key: \"1ooewy\"\n        }\n    ]\n];\nconst EyeOff = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"eye-off\", __iconNode);\n //# sourceMappingURL=eye-off.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/map-pin.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ MapPin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0\",\n            key: \"1r0f0z\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"10\",\n            r: \"3\",\n            key: \"ilqhr7\"\n        }\n    ]\n];\nconst MapPin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"map-pin\", __iconNode);\n //# sourceMappingURL=map-pin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ MousePointer)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12.586 12.586 19 19\",\n            key: \"ea5xo7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z\",\n            key: \"277e5u\"\n        }\n    ]\n];\nconst MousePointer = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"mouse-pointer\", __iconNode);\n //# sourceMappingURL=mouse-pointer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/redo.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Redo)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 7v6h-6\",\n            key: \"3ptur4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7\",\n            key: \"1kgawr\"\n        }\n    ]\n];\nconst Redo = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"redo\", __iconNode);\n //# sourceMappingURL=redo.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcmVkby5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMzQztRQUFDLENBQVE7UUFBQSxDQUFFO1lBQUEsRUFBRyw0Q0FBNkM7WUFBQSxJQUFLO1FBQVU7S0FBQTtDQUM1RTtBQWFNLFdBQU8sa0VBQWlCLFNBQVEsQ0FBVSIsInNvdXJjZXMiOlsiRjpcXHNyY1xcaWNvbnNcXHJlZG8udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMjEgN3Y2aC02Jywga2V5OiAnM3B0dXI0JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTMgMTdhOSA5IDAgMCAxIDktOSA5IDkgMCAwIDEgNiAyLjNsMyAyLjcnLCBrZXk6ICcxa2dhd3InIH1dLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFJlZG9cbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qRWdOM1kyYUMwMklpQXZQZ29nSUR4d1lYUm9JR1E5SWsweklERTNZVGtnT1NBd0lEQWdNU0E1TFRrZ09TQTVJREFnTUNBeElEWWdNaTR6YkRNZ01pNDNJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9yZWRvXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgUmVkbyA9IGNyZWF0ZUx1Y2lkZUljb24oJ3JlZG8nLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgUmVkbztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/save.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Save)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\",\n            key: \"1c8476\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\",\n            key: \"1ydtos\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 3v4a1 1 0 0 0 1 1h7\",\n            key: \"t51u73\"\n        }\n    ]\n];\nconst Save = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"save\", __iconNode);\n //# sourceMappingURL=save.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/square.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Square)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"2\",\n            key: \"afitv7\"\n        }\n    ]\n];\nconst Square = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"square\", __iconNode);\n //# sourceMappingURL=square.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc3F1YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFDbEM7UUFBQztRQUFRLENBQUU7WUFBQSxNQUFPLE1BQU07WUFBQSxNQUFRLE9BQU07WUFBQSxDQUFHLE1BQUs7WUFBQSxFQUFHLElBQUs7WUFBQSxHQUFJLElBQUs7WUFBQSxJQUFLO1FBQVU7S0FBQTtDQUNoRjtBQWFNLGFBQVMsa0VBQWlCLFdBQVUsQ0FBVSIsInNvdXJjZXMiOlsiRjpcXHNyY1xcaWNvbnNcXHNxdWFyZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsncmVjdCcsIHsgd2lkdGg6ICcxOCcsIGhlaWdodDogJzE4JywgeDogJzMnLCB5OiAnMycsIHJ4OiAnMicsIGtleTogJ2FmaXR2NycgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgU3F1YXJlXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjbVZqZENCM2FXUjBhRDBpTVRnaUlHaGxhV2RvZEQwaU1UZ2lJSGc5SWpNaUlIazlJak1pSUhKNFBTSXlJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9zcXVhcmVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBTcXVhcmUgPSBjcmVhdGVMdWNpZGVJY29uKCdzcXVhcmUnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgU3F1YXJlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/tag.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Tag)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z\",\n            key: \"vktsd0\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"7.5\",\n            cy: \"7.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"kqv944\"\n        }\n    ]\n];\nconst Tag = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"tag\", __iconNode);\n //# sourceMappingURL=tag.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/undo.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Undo)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 7v6h6\",\n            key: \"1v2h90\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13\",\n            key: \"1r6uu6\"\n        }\n    ]\n];\nconst Undo = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"undo\", __iconNode);\n //# sourceMappingURL=undo.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zoom-in.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ZoomIn)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"21\",\n            x2: \"16.65\",\n            y1: \"21\",\n            y2: \"16.65\",\n            key: \"13gj7c\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"11\",\n            x2: \"11\",\n            y1: \"8\",\n            y2: \"14\",\n            key: \"1vmskp\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"8\",\n            x2: \"14\",\n            y1: \"11\",\n            y2: \"11\",\n            key: \"durymu\"\n        }\n    ]\n];\nconst ZoomIn = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"zoom-in\", __iconNode);\n //# sourceMappingURL=zoom-in.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvem9vbS1pbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsUUFBVTtRQUFBO1lBQUUsRUFBSTtZQUFNLENBQUksUUFBTTtZQUFBLENBQUc7WUFBSyxHQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ3hEO1FBQUM7UUFBUSxDQUFFO1lBQUEsSUFBSSxDQUFNO1lBQUEsSUFBSSxDQUFTO1lBQUEsR0FBSSxLQUFNO1lBQUEsR0FBSSxRQUFTO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUN4RTtRQUFDO1FBQVEsQ0FBRTtZQUFBLElBQUksQ0FBTTtZQUFBLElBQUksQ0FBTTtZQUFBLEdBQUksSUFBSztZQUFBLEdBQUksS0FBTTtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDakU7UUFBQyxPQUFRO1FBQUE7WUFBRSxHQUFJLElBQUs7WUFBQSxHQUFJLEtBQU07WUFBQSxJQUFJLENBQU07WUFBQSxJQUFJLElBQU07WUFBQSxJQUFLO1FBQVU7S0FBQTtDQUNuRTtBQWFNLGFBQVMsa0VBQWlCLFlBQVcsQ0FBVSIsInNvdXJjZXMiOlsiRjpcXHNyY1xcaWNvbnNcXHpvb20taW4udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbJ2NpcmNsZScsIHsgY3g6ICcxMScsIGN5OiAnMTEnLCByOiAnOCcsIGtleTogJzRlajk3dScgfV0sXG4gIFsnbGluZScsIHsgeDE6ICcyMScsIHgyOiAnMTYuNjUnLCB5MTogJzIxJywgeTI6ICcxNi42NScsIGtleTogJzEzZ2o3YycgfV0sXG4gIFsnbGluZScsIHsgeDE6ICcxMScsIHgyOiAnMTEnLCB5MTogJzgnLCB5MjogJzE0Jywga2V5OiAnMXZtc2twJyB9XSxcbiAgWydsaW5lJywgeyB4MTogJzgnLCB4MjogJzE0JywgeTE6ICcxMScsIHkyOiAnMTEnLCBrZXk6ICdkdXJ5bXUnIH1dLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFpvb21JblxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4WTJseVkyeGxJR040UFNJeE1TSWdZM2s5SWpFeElpQnlQU0k0SWlBdlBnb2dJRHhzYVc1bElIZ3hQU0l5TVNJZ2VESTlJakUyTGpZMUlpQjVNVDBpTWpFaUlIa3lQU0l4Tmk0Mk5TSWdMejRLSUNBOGJHbHVaU0I0TVQwaU1URWlJSGd5UFNJeE1TSWdlVEU5SWpnaUlIa3lQU0l4TkNJZ0x6NEtJQ0E4YkdsdVpTQjRNVDBpT0NJZ2VESTlJakUwSWlCNU1UMGlNVEVpSUhreVBTSXhNU0lnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvem9vbS1pblxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFpvb21JbiA9IGNyZWF0ZUx1Y2lkZUljb24oJ3pvb20taW4nLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgWm9vbUluO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zoom-out.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ZoomOut)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"21\",\n            x2: \"16.65\",\n            y1: \"21\",\n            y2: \"16.65\",\n            key: \"13gj7c\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"8\",\n            x2: \"14\",\n            y1: \"11\",\n            y2: \"11\",\n            key: \"durymu\"\n        }\n    ]\n];\nconst ZoomOut = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"zoom-out\", __iconNode);\n //# sourceMappingURL=zoom-out.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\n"));

/***/ })

});
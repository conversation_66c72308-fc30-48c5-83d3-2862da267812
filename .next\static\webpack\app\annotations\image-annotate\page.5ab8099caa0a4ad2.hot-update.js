"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    const [globalMousePosition, setGlobalMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    // Global mouse tracking for context menu following\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const handleGlobalMouseMove = {\n                \"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\": (e)=>{\n                    setGlobalMousePosition({\n                        x: e.clientX,\n                        y: e.clientY\n                    });\n                }\n            }[\"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\"];\n            if (contextMenu.show) {\n                document.addEventListener('mousemove', handleGlobalMouseMove);\n                return ({\n                    \"ImageAnnotationCanvas.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleGlobalMouseMove);\n                    }\n                })[\"ImageAnnotationCanvas.useEffect\"];\n            }\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        contextMenu.show\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    const handleContextMenu = (e)=>{\n        var _canvasRef_current;\n        e.preventDefault();\n        const coords = getCanvasCoordinates(e);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // Check if right-clicking on an existing annotation\n        const clickedAnnotation = annotations.find((annotation)=>{\n            switch(annotation.type){\n                case 'rectangle':\n                    const [x, y, width, height] = annotation.coordinates;\n                    return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                case 'circle':\n                    const [cx, cy, radius] = annotation.coordinates;\n                    const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                    return distance <= radius;\n                case 'point':\n                    const [px, py] = annotation.coordinates;\n                    const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                    return pointDistance <= 10;\n                default:\n                    return false;\n            }\n        });\n        setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            targetAnnotation: clickedAnnotation || null,\n            clickPosition: coords\n        });\n    };\n    const hideContextMenu = ()=>{\n        setContextMenu((prev)=>({\n                ...prev,\n                show: false\n            }));\n    };\n    // Context menu actions\n    const handleDeleteAnnotation = ()=>{\n        if (contextMenu.targetAnnotation) {\n            onAnnotationDelete(contextMenu.targetAnnotation.id);\n        }\n        hideContextMenu();\n    };\n    const handleEditLabel = ()=>{\n        if (contextMenu.targetAnnotation) {\n            setEditingLabel(contextMenu.targetAnnotation.id);\n            setEditingLabelValue(contextMenu.targetAnnotation.label);\n        }\n        hideContextMenu();\n    };\n    const handleAddAnnotation = (type)=>{\n        if (!contextMenu.clickPosition) return;\n        const coords = contextMenu.clickPosition;\n        let newAnnotation;\n        if (type === 'point') {\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'rectangle') {\n            // Create a small default rectangle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x - 25,\n                    coords.y - 25,\n                    50,\n                    50\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'circle') {\n            // Create a small default circle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    25\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else {\n            return;\n        }\n        onAnnotationCreate(newAnnotation);\n        hideContextMenu();\n    };\n    const handleChangeLabel = (labelName)=>{\n        if (contextMenu.targetAnnotation) {\n            const labelConfig = predefinedLabels.find((l)=>l.name === labelName);\n            onAnnotationUpdate(contextMenu.targetAnnotation.id, {\n                label: labelName,\n                color: (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || currentColor\n            });\n        }\n        hideContextMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 493,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick,\n                onContextMenu: handleContextMenu,\n                onClick: hideContextMenu\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 536,\n                columnNumber: 9\n            }, this),\n            contextMenu.show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: hideContextMenu\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed z-50 bg-white rounded-lg shadow-lg border py-2 min-w-48 pointer-events-auto\",\n                        style: {\n                            left: globalMousePosition.x + 10,\n                            top: globalMousePosition.y + 10,\n                            transform: 'translate(0, 0)'\n                        },\n                        children: contextMenu.targetAnnotation ? // Menu for existing annotation\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: [\n                                        contextMenu.targetAnnotation.label,\n                                        \" (\",\n                                        contextMenu.targetAnnotation.type,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: handleEditLabel,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"编辑标签\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1 text-xs text-gray-500\",\n                                    children: \"更改标签为:\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 17\n                                }, this),\n                                predefinedLabels.map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full px-3 py-1 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                        onClick: ()=>handleChangeLabel(label.name),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded mr-2 border\",\n                                                style: {\n                                                    backgroundColor: label.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 21\n                                            }, this),\n                                            label.name\n                                        ]\n                                    }, label.name, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t my-1\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center\",\n                                    onClick: handleDeleteAnnotation,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDDD1️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"删除标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : // Menu for empty area\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: \"添加标注\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('point'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDCCD\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加点标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('rectangle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⬜\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加矩形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('circle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⭕\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加圆形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"IDHObakyPsHr+JpRBpdLTwKuRL0=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
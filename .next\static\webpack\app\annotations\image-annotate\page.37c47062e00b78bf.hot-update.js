"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    const handleContextMenu = (e)=>{\n        var _canvasRef_current;\n        e.preventDefault();\n        const coords = getCanvasCoordinates(e);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // Check if right-clicking on an existing annotation\n        const clickedAnnotation = annotations.find((annotation)=>{\n            switch(annotation.type){\n                case 'rectangle':\n                    const [x, y, width, height] = annotation.coordinates;\n                    return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                case 'circle':\n                    const [cx, cy, radius] = annotation.coordinates;\n                    const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                    return distance <= radius;\n                case 'point':\n                    const [px, py] = annotation.coordinates;\n                    const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                    return pointDistance <= 10;\n                default:\n                    return false;\n            }\n        });\n        setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            targetAnnotation: clickedAnnotation || null,\n            clickPosition: coords\n        });\n    };\n    const hideContextMenu = ()=>{\n        setContextMenu((prev)=>({\n                ...prev,\n                show: false\n            }));\n    };\n    // Context menu actions\n    const handleDeleteAnnotation = ()=>{\n        if (contextMenu.targetAnnotation) {\n            onAnnotationDelete(contextMenu.targetAnnotation.id);\n        }\n        hideContextMenu();\n    };\n    const handleEditLabel = ()=>{\n        if (contextMenu.targetAnnotation) {\n            setEditingLabel(contextMenu.targetAnnotation.id);\n            setEditingLabelValue(contextMenu.targetAnnotation.label);\n        }\n        hideContextMenu();\n    };\n    const handleAddAnnotation = (type)=>{\n        if (!contextMenu.clickPosition) return;\n        const coords = contextMenu.clickPosition;\n        let newAnnotation;\n        if (type === 'point') {\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'rectangle') {\n            // Create a small default rectangle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x - 25,\n                    coords.y - 25,\n                    50,\n                    50\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'circle') {\n            // Create a small default circle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    25\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else {\n            return;\n        }\n        onAnnotationCreate(newAnnotation);\n        hideContextMenu();\n    };\n    const handleChangeLabel = (labelName)=>{\n        if (contextMenu.targetAnnotation) {\n            const labelConfig = predefinedLabels.find((l)=>l.name === labelName);\n            onAnnotationUpdate(contextMenu.targetAnnotation.id, {\n                label: labelName,\n                color: (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || currentColor\n            });\n        }\n        hideContextMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 478,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick,\n                onContextMenu: handleContextMenu,\n                onClick: hideContextMenu\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 522,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 521,\n                columnNumber: 9\n            }, this),\n            contextMenu.show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: hideContextMenu\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed z-50 bg-white rounded-lg shadow-lg border py-2 min-w-48\",\n                        style: {\n                            left: contextMenu.x,\n                            top: contextMenu.y\n                        },\n                        children: contextMenu.targetAnnotation ? // Menu for existing annotation\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: [\n                                        contextMenu.targetAnnotation.label,\n                                        \" (\",\n                                        contextMenu.targetAnnotation.type,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: handleEditLabel,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"编辑标签\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1 text-xs text-gray-500\",\n                                    children: \"更改标签为:\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 17\n                                }, this),\n                                predefinedLabels.map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full px-3 py-1 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                        onClick: ()=>handleChangeLabel(label.name),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded mr-2 border\",\n                                                style: {\n                                                    backgroundColor: label.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 21\n                                            }, this),\n                                            label.name\n                                        ]\n                                    }, label.name, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t my-1\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center\",\n                                    onClick: handleDeleteAnnotation,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDDD1️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"删除标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : // Menu for empty area\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: \"添加标注\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('point'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDCCD\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加点标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('rectangle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⬜\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加矩形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('circle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⭕\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加圆形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 473,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"fbV+AjM4vIIwRHQRKPy9Hvtsk98=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
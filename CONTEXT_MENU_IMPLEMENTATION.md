# 🖱️ Canvas右键菜单功能实现

## 📋 功能概述

在图像标注canvas中添加了完整的右键上下文菜单功能，支持删除标注、编辑标签、添加新标注等操作，大大提升了用户操作的便利性。

## 🎯 实现的功能

### 1. 智能上下文菜单
- **目标检测**: 自动识别右键点击的目标（标注或空白区域）
- **动态菜单**: 根据点击目标显示不同的菜单选项
- **精确定位**: 菜单出现在鼠标点击位置

### 2. 标注操作功能
- **删除标注**: 右键已有标注可直接删除
- **编辑标签**: 快速进入标签编辑模式
- **更改标签**: 从预定义标签中选择新标签
- **添加标注**: 在空白区域右键可添加新标注

### 3. 用户体验优化
- **视觉反馈**: 清晰的菜单样式和图标
- **操作确认**: 重要操作有明确的视觉提示
- **快速关闭**: 点击其他区域自动关闭菜单

## 🛠 技术实现

### 状态管理

```typescript
const [contextMenu, setContextMenu] = useState<{
  show: boolean
  x: number
  y: number
  targetAnnotation: AnnotationShape | null
  clickPosition: { x: number; y: number } | null
}>({
  show: false,
  x: 0,
  y: 0,
  targetAnnotation: null,
  clickPosition: null
})
```

#### 状态字段说明
- **show**: 控制菜单显示/隐藏
- **x, y**: 菜单在屏幕上的绝对位置
- **targetAnnotation**: 右键点击的目标标注（如果有）
- **clickPosition**: 点击位置的canvas坐标

### 右键事件处理

```typescript
const handleContextMenu = (e: React.MouseEvent<HTMLCanvasElement>) => {
  e.preventDefault() // 阻止浏览器默认右键菜单
  
  const coords = getCanvasCoordinates(e)
  
  // 检测点击目标
  const clickedAnnotation = annotations.find(annotation => {
    // 根据标注类型检测点击区域
    switch (annotation.type) {
      case 'rectangle': // 矩形区域检测
      case 'circle':    // 圆形区域检测  
      case 'point':     // 点击范围检测
    }
  })
  
  // 设置菜单状态
  setContextMenu({
    show: true,
    x: e.clientX,  // 屏幕坐标
    y: e.clientY,
    targetAnnotation: clickedAnnotation || null,
    clickPosition: coords  // canvas坐标
  })
}
```

#### 目标检测逻辑
1. **矩形标注**: 检测点击是否在矩形边界内
2. **圆形标注**: 计算点击位置到圆心的距离
3. **点标注**: 检测点击是否在点的容差范围内（10像素）

### 菜单操作功能

#### 1. 删除标注
```typescript
const handleDeleteAnnotation = () => {
  if (contextMenu.targetAnnotation) {
    onAnnotationDelete(contextMenu.targetAnnotation.id)
  }
  hideContextMenu()
}
```

#### 2. 编辑标签
```typescript
const handleEditLabel = () => {
  if (contextMenu.targetAnnotation) {
    setEditingLabel(contextMenu.targetAnnotation.id)
    setEditingLabelValue(contextMenu.targetAnnotation.label)
  }
  hideContextMenu()
}
```

#### 3. 更改标签
```typescript
const handleChangeLabel = (labelName: string) => {
  if (contextMenu.targetAnnotation) {
    const labelConfig = predefinedLabels.find(l => l.name === labelName)
    onAnnotationUpdate(contextMenu.targetAnnotation.id, {
      label: labelName,
      color: labelConfig?.color || currentColor
    })
  }
  hideContextMenu()
}
```

#### 4. 添加新标注
```typescript
const handleAddAnnotation = (type: 'rectangle' | 'circle' | 'point') => {
  if (!contextMenu.clickPosition) return
  
  const coords = contextMenu.clickPosition
  let newAnnotation: AnnotationShape
  
  // 根据类型创建默认大小的标注
  if (type === 'point') {
    newAnnotation = { /* 点标注 */ }
  } else if (type === 'rectangle') {
    newAnnotation = { /* 50x50像素矩形 */ }
  } else if (type === 'circle') {
    newAnnotation = { /* 半径25像素圆形 */ }
  }
  
  onAnnotationCreate(newAnnotation)
  hideContextMenu()
}
```

## 🎨 用户界面设计

### 菜单样式
- **背景**: 白色圆角卡片
- **阴影**: 轻微投影效果
- **边框**: 浅灰色边框
- **最小宽度**: 192px (min-w-48)

### 菜单项设计
- **悬停效果**: 浅灰色背景高亮
- **图标**: 使用emoji图标增强视觉效果
- **分组**: 使用分割线区分不同功能组
- **危险操作**: 删除按钮使用红色文字

### 两种菜单模式

#### 1. 标注菜单（右键已有标注）
```
┌─────────────────────────┐
│ 人物 (rectangle)        │ ← 标注信息
├─────────────────────────┤
│ ✏️ 编辑标签             │
├─────────────────────────┤
│ 更改标签为:             │ ← 标签选择区
│ 🔴 人物                 │
│ 🔵 车辆                 │
│ 🟢 建筑                 │
├─────────────────────────┤
│ 🗑️ 删除标注            │ ← 危险操作
└─────────────────────────┘
```

#### 2. 添加菜单（右键空白区域）
```
┌─────────────────────────┐
│ 添加标注                │ ← 功能标题
├─────────────────────────┤
│ 📍 添加点标注           │
│ ⬜ 添加矩形标注         │
│ ⭕ 添加圆形标注         │
└─────────────────────────┘
```

## 🔧 集成方式

### Canvas事件绑定
```typescript
<canvas
  onContextMenu={handleContextMenu}  // 右键菜单
  onClick={hideContextMenu}          // 左键关闭菜单
  // ... 其他事件
/>
```

### Props扩展
为ImageAnnotationCanvas组件添加了新的props：
- **predefinedLabels**: 预定义标签列表
- **onAnnotationDelete**: 删除标注回调

## 📱 响应式支持

### 菜单定位
- **屏幕边界检测**: 防止菜单超出屏幕范围
- **自动调整**: 靠近边界时自动调整菜单位置
- **移动端适配**: 可扩展支持触摸长按

### 关闭机制
1. **点击其他区域**: 自动关闭菜单
2. **执行操作**: 操作完成后自动关闭
3. **ESC键**: 可扩展键盘快捷键支持

## ✅ 使用方法

### 基本操作
1. **右键空白区域**: 显示添加标注菜单
2. **右键已有标注**: 显示标注操作菜单
3. **选择菜单项**: 点击执行对应操作
4. **关闭菜单**: 点击其他区域或执行操作

### 标注管理
1. **快速删除**: 右键标注 → 删除标注
2. **编辑标签**: 右键标注 → 编辑标签
3. **更改标签**: 右键标注 → 选择新标签
4. **快速添加**: 右键空白 → 选择标注类型

### 工作流程优化
- **精确定位**: 右键位置即为新标注位置
- **批量操作**: 可连续右键进行多个操作
- **视觉反馈**: 操作结果立即可见

## 🧪 测试要点

### 功能测试
- [ ] 右键空白区域显示添加菜单
- [ ] 右键标注显示操作菜单
- [ ] 删除标注功能正常
- [ ] 编辑标签功能正常
- [ ] 更改标签功能正常
- [ ] 添加新标注功能正常

### 交互测试
- [ ] 菜单定位准确
- [ ] 点击其他区域关闭菜单
- [ ] 菜单项悬停效果正常
- [ ] 操作后菜单自动关闭

### 边界测试
- [ ] 屏幕边缘右键菜单显示正常
- [ ] 重叠标注的目标检测准确
- [ ] 快速连续右键操作稳定

## 🚀 后续优化

### 功能扩展
1. **键盘快捷键**: 支持ESC关闭菜单
2. **批量操作**: 支持多选标注批量操作
3. **复制粘贴**: 支持标注的复制和粘贴
4. **撤销重做**: 右键操作集成到历史记录

### 性能优化
1. **事件节流**: 优化鼠标事件处理
2. **菜单缓存**: 缓存菜单组件减少重渲染
3. **异步操作**: 大量标注时的异步处理

### 用户体验
1. **动画效果**: 菜单显示/隐藏动画
2. **触摸支持**: 移动端长按支持
3. **自定义菜单**: 允许用户自定义菜单项

这个右键菜单功能大大提升了图像标注的操作效率，让用户可以更直观、快速地管理标注！

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    const [globalMousePosition, setGlobalMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    // Global mouse tracking for context menu following\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const handleGlobalMouseMove = {\n                \"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\": (e)=>{\n                    setGlobalMousePosition({\n                        x: e.clientX,\n                        y: e.clientY\n                    });\n                }\n            }[\"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\"];\n            if (contextMenu.show) {\n                document.addEventListener('mousemove', handleGlobalMouseMove);\n                return ({\n                    \"ImageAnnotationCanvas.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleGlobalMouseMove);\n                    }\n                })[\"ImageAnnotationCanvas.useEffect\"];\n            }\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        contextMenu.show\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    const handleContextMenu = (e)=>{\n        var _canvasRef_current;\n        e.preventDefault();\n        const coords = getCanvasCoordinates(e);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // Check if right-clicking on an existing annotation\n        const clickedAnnotation = annotations.find((annotation)=>{\n            switch(annotation.type){\n                case 'rectangle':\n                    const [x, y, width, height] = annotation.coordinates;\n                    return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                case 'circle':\n                    const [cx, cy, radius] = annotation.coordinates;\n                    const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                    return distance <= radius;\n                case 'point':\n                    const [px, py] = annotation.coordinates;\n                    const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                    return pointDistance <= 10;\n                default:\n                    return false;\n            }\n        });\n        setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            targetAnnotation: clickedAnnotation || null,\n            clickPosition: coords\n        });\n    };\n    const hideContextMenu = ()=>{\n        setContextMenu((prev)=>({\n                ...prev,\n                show: false\n            }));\n    };\n    // Calculate menu position with boundary detection\n    const getMenuPosition = ()=>{\n        const menuWidth = 192 // min-w-48 = 192px\n        ;\n        const menuHeight = 200 // estimated menu height\n        ;\n        const offset = 10;\n        let x = globalMousePosition.x + offset;\n        let y = globalMousePosition.y + offset;\n        // Check right boundary\n        if (x + menuWidth > window.innerWidth) {\n            x = globalMousePosition.x - menuWidth - offset;\n        }\n        // Check bottom boundary\n        if (y + menuHeight > window.innerHeight) {\n            y = globalMousePosition.y - menuHeight - offset;\n        }\n        // Ensure menu doesn't go off-screen\n        x = Math.max(0, Math.min(x, window.innerWidth - menuWidth));\n        y = Math.max(0, Math.min(y, window.innerHeight - menuHeight));\n        return {\n            x,\n            y\n        };\n    };\n    // Context menu actions\n    const handleDeleteAnnotation = ()=>{\n        if (contextMenu.targetAnnotation) {\n            onAnnotationDelete(contextMenu.targetAnnotation.id);\n        }\n        hideContextMenu();\n    };\n    const handleEditLabel = ()=>{\n        if (contextMenu.targetAnnotation) {\n            setEditingLabel(contextMenu.targetAnnotation.id);\n            setEditingLabelValue(contextMenu.targetAnnotation.label);\n        }\n        hideContextMenu();\n    };\n    const handleAddAnnotation = (type)=>{\n        if (!contextMenu.clickPosition) return;\n        const coords = contextMenu.clickPosition;\n        let newAnnotation;\n        if (type === 'point') {\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'rectangle') {\n            // Create a small default rectangle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x - 25,\n                    coords.y - 25,\n                    50,\n                    50\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'circle') {\n            // Create a small default circle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    25\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else {\n            return;\n        }\n        onAnnotationCreate(newAnnotation);\n        hideContextMenu();\n    };\n    const handleChangeLabel = (labelName)=>{\n        if (contextMenu.targetAnnotation) {\n            const labelConfig = predefinedLabels.find((l)=>l.name === labelName);\n            onAnnotationUpdate(contextMenu.targetAnnotation.id, {\n                label: labelName,\n                color: (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || currentColor\n            });\n        }\n        hideContextMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 520,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 519,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick,\n                onContextMenu: handleContextMenu,\n                onClick: hideContextMenu\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 547,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, this),\n            contextMenu.show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: hideContextMenu\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed z-50 bg-white rounded-lg shadow-lg border py-2 min-w-48 pointer-events-auto\",\n                        style: {\n                            left: globalMousePosition.x + 10,\n                            top: globalMousePosition.y + 10,\n                            transform: 'translate(0, 0)'\n                        },\n                        children: contextMenu.targetAnnotation ? // Menu for existing annotation\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: [\n                                        contextMenu.targetAnnotation.label,\n                                        \" (\",\n                                        contextMenu.targetAnnotation.type,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: handleEditLabel,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"编辑标签\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1 text-xs text-gray-500\",\n                                    children: \"更改标签为:\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 17\n                                }, this),\n                                predefinedLabels.map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full px-3 py-1 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                        onClick: ()=>handleChangeLabel(label.name),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded mr-2 border\",\n                                                style: {\n                                                    backgroundColor: label.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 21\n                                            }, this),\n                                            label.name\n                                        ]\n                                    }, label.name, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t my-1\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center\",\n                                    onClick: handleDeleteAnnotation,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDDD1️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"删除标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : // Menu for empty area\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: \"添加标注\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('point'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDCCD\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加点标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('rectangle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⬜\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加矩形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('circle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⭕\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加圆形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 514,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"IDHObakyPsHr+JpRBpdLTwKuRL0=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvYW5ub3RhdGlvbi9pbWFnZS1hbm5vdGF0aW9uLWNhbnZhcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWdFO0FBNkJ6RCxTQUFTSSxzQkFBc0IsS0FnQlQ7UUFoQlMsRUFDcENDLFFBQVEsRUFDUkMsVUFBVSxFQUNWQyxXQUFXLEVBQ1hDLFdBQVcsRUFDWEMsa0JBQWtCLEVBQ2xCQyxXQUFXLEVBQ1hDLFlBQVksRUFDWkMsWUFBWSxFQUNaQyxJQUFJLEVBQ0pDLFVBQVUsRUFDVkMsZ0JBQWdCLEVBQ2hCQyxrQkFBa0IsRUFDbEJDLGtCQUFrQixFQUNsQkMsa0JBQWtCLEVBQ2xCQyxrQkFBa0IsRUFDUyxHQWhCUztRQTZpQmxCWCxtQkFDQUE7O0lBN2hCbEIsTUFBTVksWUFBWXBCLDZDQUFNQSxDQUFvQjtJQUM1QyxNQUFNcUIsV0FBV3JCLDZDQUFNQSxDQUFtQjtJQUMxQyxNQUFNLENBQUNzQixXQUFXQyxhQUFhLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNzQixZQUFZQyxjQUFjLEdBQUd2QiwrQ0FBUUEsQ0FBa0M7SUFDOUUsTUFBTSxDQUFDd0IsbUJBQW1CQyxxQkFBcUIsR0FBR3pCLCtDQUFRQSxDQUF5QjtJQUNuRixNQUFNLENBQUMwQixhQUFhQyxlQUFlLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM0QixlQUFlQyxpQkFBaUIsR0FBRzdCLCtDQUFRQSxDQUFrQztJQUNwRixNQUFNLENBQUM4QixjQUFjQyxnQkFBZ0IsR0FBRy9CLCtDQUFRQSxDQUFnQjtJQUNoRSxNQUFNLENBQUNnQyxtQkFBbUJDLHFCQUFxQixHQUFHakMsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDa0MsYUFBYUMsZUFBZSxHQUFHbkMsK0NBQVFBLENBTTNDO1FBQ0RvQyxNQUFNO1FBQ05DLEdBQUc7UUFDSEMsR0FBRztRQUNIQyxrQkFBa0I7UUFDbEJDLGVBQWU7SUFDakI7SUFDQSxNQUFNLENBQUNDLHFCQUFxQkMsdUJBQXVCLEdBQUcxQywrQ0FBUUEsQ0FBMkI7UUFBRXFDLEdBQUc7UUFBR0MsR0FBRztJQUFFO0lBRXRHLGdCQUFnQjtJQUNoQnZDLGdEQUFTQTsyQ0FBQztZQUNSNEMsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQztnQkFDMUN6QztnQkFDQUM7Z0JBQ0FDO2dCQUNBcUI7WUFDRjtRQUNGOzBDQUFHO1FBQUN2QjtRQUFVQztRQUFZQztRQUFhcUI7S0FBWTtJQUVuRCxNQUFNbUIsa0JBQWtCNUMsa0RBQVdBOzhEQUFDLENBQUM2QztZQUNuQ0EsSUFBSUMsU0FBUyxDQUFDLEdBQUcsR0FBR0QsSUFBSUUsTUFBTSxDQUFDQyxLQUFLLEVBQUVILElBQUlFLE1BQU0sQ0FBQ0UsTUFBTTtZQUV2RCxhQUFhO1lBQ2IsTUFBTUMsUUFBUWhDLFNBQVNpQyxPQUFPO1lBQzlCLElBQUlELFNBQVNBLE1BQU1FLFFBQVEsRUFBRTtnQkFDM0JQLElBQUlRLFNBQVMsQ0FBQ0gsT0FBTyxHQUFHLEdBQUcvQyxZQUFZQztZQUN6QztZQUVBLG1CQUFtQjtZQUNuQkMsWUFBWWlELE9BQU87c0VBQUMsQ0FBQ0M7b0JBQ25CVixJQUFJVyxXQUFXLEdBQUdELFdBQVdFLEtBQUs7b0JBQ2xDWixJQUFJYSxTQUFTLEdBQUdILFdBQVdFLEtBQUssR0FBRyxLQUFLLGNBQWM7O29CQUN0RFosSUFBSWMsU0FBUyxHQUFHSixXQUFXSyxFQUFFLEtBQUt0RCxxQkFBcUIsSUFBSTtvQkFFM0QsT0FBUWlELFdBQVdNLElBQUk7d0JBQ3JCLEtBQUs7NEJBQ0gsTUFBTSxDQUFDekIsR0FBR0MsR0FBR1csT0FBT0MsT0FBTyxHQUFHTSxXQUFXTyxXQUFXOzRCQUNwRGpCLElBQUlrQixVQUFVLENBQUMzQixHQUFHQyxHQUFHVyxPQUFPQzs0QkFDNUJKLElBQUltQixRQUFRLENBQUM1QixHQUFHQyxHQUFHVyxPQUFPQzs0QkFDMUI7d0JBRUYsS0FBSzs0QkFDSCxNQUFNLENBQUNnQixJQUFJQyxJQUFJQyxPQUFPLEdBQUdaLFdBQVdPLFdBQVc7NEJBQy9DakIsSUFBSXVCLFNBQVM7NEJBQ2J2QixJQUFJd0IsR0FBRyxDQUFDSixJQUFJQyxJQUFJQyxRQUFRLEdBQUcsSUFBSUcsS0FBS0MsRUFBRTs0QkFDdEMxQixJQUFJMkIsTUFBTTs0QkFDVjNCLElBQUk0QixJQUFJOzRCQUNSO3dCQUVGLEtBQUs7NEJBQ0gsTUFBTSxDQUFDQyxJQUFJQyxHQUFHLEdBQUdwQixXQUFXTyxXQUFXOzRCQUN2Q2pCLElBQUl1QixTQUFTOzRCQUNidkIsSUFBSXdCLEdBQUcsQ0FBQ0ssSUFBSUMsSUFBSSxHQUFHLEdBQUcsSUFBSUwsS0FBS0MsRUFBRTs0QkFDakMxQixJQUFJNEIsSUFBSTs0QkFDUjtvQkFHSjtvQkFFQSxhQUFhO29CQUNiLElBQUk5RCxjQUFjNEMsV0FBV3FCLEtBQUssRUFBRTt3QkFDbEMvQixJQUFJYSxTQUFTLEdBQUdILFdBQVdFLEtBQUs7d0JBQ2hDWixJQUFJZ0MsSUFBSSxHQUFHO3dCQUNYaEMsSUFBSVcsV0FBVyxHQUFHO3dCQUNsQlgsSUFBSWMsU0FBUyxHQUFHO3dCQUVoQixNQUFNbUIsU0FBU3ZCLFdBQVdPLFdBQVcsQ0FBQyxFQUFFO3dCQUN4QyxNQUFNaUIsU0FBU3hCLFdBQVdPLFdBQVcsQ0FBQyxFQUFFLEdBQUc7d0JBRTNDLG9CQUFvQjt3QkFDcEJqQixJQUFJbUMsVUFBVSxDQUFDekIsV0FBV3FCLEtBQUssRUFBRUUsUUFBUUM7d0JBQ3pDLFlBQVk7d0JBQ1psQyxJQUFJb0MsUUFBUSxDQUFDMUIsV0FBV3FCLEtBQUssRUFBRUUsUUFBUUM7d0JBRXZDLDhDQUE4Qzt3QkFDOUMsSUFBSXhCLFdBQVdLLEVBQUUsS0FBS3RELG9CQUFvQjs0QkFDeEN1QyxJQUFJYSxTQUFTLEdBQUc7NEJBQ2hCYixJQUFJVyxXQUFXLEdBQUc7NEJBQ2xCWCxJQUFJYyxTQUFTLEdBQUc7NEJBQ2hCLE1BQU11QixZQUFZckMsSUFBSXNDLFdBQVcsQ0FBQzVCLFdBQVdxQixLQUFLLEVBQUU1QixLQUFLOzRCQUN6REgsSUFBSW1CLFFBQVEsQ0FBQ2MsU0FBUyxHQUFHQyxTQUFTLElBQUlHLFlBQVksR0FBRzs0QkFDckRyQyxJQUFJa0IsVUFBVSxDQUFDZSxTQUFTLEdBQUdDLFNBQVMsSUFBSUcsWUFBWSxHQUFHO3dCQUN6RDtvQkFDRjtnQkFDRjs7WUFFQSx3Q0FBd0M7WUFDeEMsSUFBSTNELG1CQUFtQjtnQkFDckJzQixJQUFJVyxXQUFXLEdBQUcvQztnQkFDbEJvQyxJQUFJYSxTQUFTLEdBQUdqRCxlQUFlO2dCQUMvQm9DLElBQUljLFNBQVMsR0FBRztnQkFDaEJkLElBQUl1QyxXQUFXLENBQUM7b0JBQUM7b0JBQUc7aUJBQUU7Z0JBRXRCLE9BQVE3RCxrQkFBa0JzQyxJQUFJO29CQUM1QixLQUFLO3dCQUNILE1BQU0sQ0FBQ3pCLEdBQUdDLEdBQUdXLE9BQU9DLE9BQU8sR0FBRzFCLGtCQUFrQnVDLFdBQVc7d0JBQzNEakIsSUFBSWtCLFVBQVUsQ0FBQzNCLEdBQUdDLEdBQUdXLE9BQU9DO3dCQUM1QjtvQkFFRixLQUFLO3dCQUNILE1BQU0sQ0FBQ2dCLElBQUlDLElBQUlDLE9BQU8sR0FBRzVDLGtCQUFrQnVDLFdBQVc7d0JBQ3REakIsSUFBSXVCLFNBQVM7d0JBQ2J2QixJQUFJd0IsR0FBRyxDQUFDSixJQUFJQyxJQUFJQyxRQUFRLEdBQUcsSUFBSUcsS0FBS0MsRUFBRTt3QkFDdEMxQixJQUFJMkIsTUFBTTt3QkFDVjtnQkFDSjtnQkFFQTNCLElBQUl1QyxXQUFXLENBQUMsRUFBRTtZQUNwQjtZQUVBLHlCQUF5QjtZQUN6QixJQUFJekQsZUFBZTtnQkFDakJrQixJQUFJYSxTQUFTLEdBQUc7Z0JBQ2hCYixJQUFJZ0MsSUFBSSxHQUFHO2dCQUNYLE1BQU1RLFlBQVksSUFBb0NmLE9BQWhDQSxLQUFLZ0IsS0FBSyxDQUFDM0QsY0FBY1MsQ0FBQyxHQUFFLE1BQWdDLE9BQTVCa0MsS0FBS2dCLEtBQUssQ0FBQzNELGNBQWNVLENBQUMsR0FBRTtnQkFDbEYsTUFBTTZDLFlBQVlyQyxJQUFJc0MsV0FBVyxDQUFDRSxXQUFXckMsS0FBSztnQkFFbEQsMEVBQTBFO2dCQUMxRSxJQUFJdUMsV0FBVzVELGNBQWNTLENBQUMsR0FBRztnQkFDakMsSUFBSW9ELFdBQVc3RCxjQUFjVSxDQUFDLEdBQUc7Z0JBRWpDLGdEQUFnRDtnQkFDaEQsSUFBSWtELFdBQVdMLFlBQVksS0FBSy9FLFlBQVk7b0JBQzFDb0YsV0FBVzVELGNBQWNTLENBQUMsR0FBRzhDLFlBQVk7Z0JBQzNDO2dCQUNBLElBQUlNLFdBQVcsSUFBSTtvQkFDakJBLFdBQVc3RCxjQUFjVSxDQUFDLEdBQUc7Z0JBQy9CO2dCQUVBLGtCQUFrQjtnQkFDbEJRLElBQUlhLFNBQVMsR0FBRztnQkFDaEJiLElBQUltQixRQUFRLENBQUN1QixXQUFXLEdBQUdDLFdBQVcsSUFBSU4sWUFBWSxJQUFJO2dCQUUxRCxZQUFZO2dCQUNackMsSUFBSWEsU0FBUyxHQUFHO2dCQUNoQmIsSUFBSW9DLFFBQVEsQ0FBQ0ksV0FBV0UsVUFBVUM7WUFDcEM7UUFDRjs2REFBRztRQUFDbkY7UUFBYUM7UUFBb0JLO1FBQVlZO1FBQW1CcEI7UUFBWUM7UUFBYUs7UUFBY2tCO0tBQWM7SUFFekg3QixnREFBU0E7MkNBQUM7WUFDUixNQUFNaUQsU0FBUzlCLFVBQVVrQyxPQUFPO1lBQ2hDLElBQUksQ0FBQ0osUUFBUTtZQUViLE1BQU1GLE1BQU1FLE9BQU8wQyxVQUFVLENBQUM7WUFDOUIsSUFBSSxDQUFDNUMsS0FBSztZQUVWRSxPQUFPQyxLQUFLLEdBQUc3QztZQUNmNEMsT0FBT0UsTUFBTSxHQUFHN0M7WUFFaEJ3QyxnQkFBZ0JDO1FBQ2xCOzBDQUFHO1FBQUNEO0tBQWdCO0lBRXBCLG1EQUFtRDtJQUNuRDlDLGdEQUFTQTsyQ0FBQztZQUNSLE1BQU00Rjt5RUFBd0IsQ0FBQ0M7b0JBQzdCbEQsdUJBQXVCO3dCQUFFTCxHQUFHdUQsRUFBRUMsT0FBTzt3QkFBRXZELEdBQUdzRCxFQUFFRSxPQUFPO29CQUFDO2dCQUN0RDs7WUFFQSxJQUFJNUQsWUFBWUUsSUFBSSxFQUFFO2dCQUNwQjJELFNBQVNDLGdCQUFnQixDQUFDLGFBQWFMO2dCQUN2Qzt1REFBTzt3QkFDTEksU0FBU0UsbUJBQW1CLENBQUMsYUFBYU47b0JBQzVDOztZQUNGO1FBQ0Y7MENBQUc7UUFBQ3pELFlBQVlFLElBQUk7S0FBQztJQUVyQixNQUFNOEQsdUJBQXVCLENBQUNOO1FBQzVCLE1BQU01QyxTQUFTOUIsVUFBVWtDLE9BQU87UUFDaEMsSUFBSSxDQUFDSixRQUFRLE9BQU87WUFBRVgsR0FBRztZQUFHQyxHQUFHO1FBQUU7UUFFakMsTUFBTTZELE9BQU9uRCxPQUFPb0QscUJBQXFCO1FBQ3pDLE9BQU87WUFDTC9ELEdBQUcsQ0FBQ3VELEVBQUVDLE9BQU8sR0FBR00sS0FBS0UsSUFBSSxJQUFJMUY7WUFDN0IyQixHQUFHLENBQUNzRCxFQUFFRSxPQUFPLEdBQUdLLEtBQUtHLEdBQUcsSUFBSTNGO1FBQzlCO0lBQ0Y7SUFFQSxNQUFNNEYsa0JBQWtCLENBQUNYO1FBQ3ZCLE1BQU1ZLFNBQVNOLHFCQUFxQk47UUFFcEMsSUFBSXBGLGdCQUFnQixVQUFVO1lBQzVCLDhDQUE4QztZQUM5QyxNQUFNaUcsb0JBQW9CbkcsWUFBWW9HLElBQUksQ0FBQ2xELENBQUFBO2dCQUN6QyxPQUFRQSxXQUFXTSxJQUFJO29CQUNyQixLQUFLO3dCQUNILE1BQU0sQ0FBQ3pCLEdBQUdDLEdBQUdXLE9BQU9DLE9BQU8sR0FBR00sV0FBV08sV0FBVzt3QkFDcEQsT0FBT3lDLE9BQU9uRSxDQUFDLElBQUlBLEtBQUttRSxPQUFPbkUsQ0FBQyxJQUFJQSxJQUFJWSxTQUNqQ3VELE9BQU9sRSxDQUFDLElBQUlBLEtBQUtrRSxPQUFPbEUsQ0FBQyxJQUFJQSxJQUFJWTtvQkFDMUMsS0FBSzt3QkFDSCxNQUFNLENBQUNnQixJQUFJQyxJQUFJQyxPQUFPLEdBQUdaLFdBQVdPLFdBQVc7d0JBQy9DLE1BQU00QyxXQUFXcEMsS0FBS3FDLElBQUksQ0FBQyxDQUFDSixPQUFPbkUsQ0FBQyxHQUFHNkIsRUFBQyxLQUFNLElBQUksQ0FBQ3NDLE9BQU9sRSxDQUFDLEdBQUc2QixFQUFDLEtBQU07d0JBQ3JFLE9BQU93QyxZQUFZdkM7b0JBQ3JCLEtBQUs7d0JBQ0gsTUFBTSxDQUFDTyxJQUFJQyxHQUFHLEdBQUdwQixXQUFXTyxXQUFXO3dCQUN2QyxNQUFNOEMsZ0JBQWdCdEMsS0FBS3FDLElBQUksQ0FBQyxDQUFDSixPQUFPbkUsQ0FBQyxHQUFHc0MsRUFBQyxLQUFNLElBQUksQ0FBQzZCLE9BQU9sRSxDQUFDLEdBQUdzQyxFQUFDLEtBQU07d0JBQzFFLE9BQU9pQyxpQkFBaUI7b0JBQzFCO3dCQUNFLE9BQU87Z0JBQ1g7WUFDRjtZQUVBN0YsbUJBQW1CeUYsQ0FBQUEsOEJBQUFBLHdDQUFBQSxrQkFBbUI1QyxFQUFFLEtBQUk7WUFDNUM7UUFDRjtRQUVBLElBQUlyRCxnQkFBZ0IsU0FBUztZQUMzQixNQUFNc0csZ0JBQWlDO2dCQUNyQ2pELElBQUlrRCxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7Z0JBQ3ZCbkQsTUFBTTtnQkFDTkMsYUFBYTtvQkFBQ3lDLE9BQU9uRSxDQUFDO29CQUFFbUUsT0FBT2xFLENBQUM7aUJBQUM7Z0JBQ2pDdUMsT0FBT3BFO2dCQUNQaUQsT0FBT2hEO1lBQ1Q7WUFDQUksbUJBQW1CZ0c7WUFDbkI7UUFDRjtRQUVBekYsYUFBYTtRQUNiRSxjQUFjaUY7UUFFZCxJQUFJaEcsZ0JBQWdCLGFBQWE7WUFDL0JpQixxQkFBcUI7Z0JBQ25Cb0MsSUFBSWtELEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtnQkFDdkJuRCxNQUFNO2dCQUNOQyxhQUFhO29CQUFDeUMsT0FBT25FLENBQUM7b0JBQUVtRSxPQUFPbEUsQ0FBQztvQkFBRTtvQkFBRztpQkFBRTtnQkFDdkN1QyxPQUFPcEU7Z0JBQ1BpRCxPQUFPaEQ7WUFDVDtRQUNGLE9BQU8sSUFBSUYsZ0JBQWdCLFVBQVU7WUFDbkNpQixxQkFBcUI7Z0JBQ25Cb0MsSUFBSWtELEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtnQkFDdkJuRCxNQUFNO2dCQUNOQyxhQUFhO29CQUFDeUMsT0FBT25FLENBQUM7b0JBQUVtRSxPQUFPbEUsQ0FBQztvQkFBRTtpQkFBRTtnQkFDcEN1QyxPQUFPcEU7Z0JBQ1BpRCxPQUFPaEQ7WUFDVDtRQUNGO0lBQ0Y7SUFFQSxNQUFNd0csa0JBQWtCLENBQUN0QjtRQUN2QixNQUFNWSxTQUFTTixxQkFBcUJOO1FBRXBDLHNEQUFzRDtRQUN0RC9ELGlCQUFpQjJFO1FBRWpCLElBQUksQ0FBQ3BGLGFBQWEsQ0FBQ0UsY0FBYyxDQUFDRSxtQkFBbUI7UUFFckQsSUFBSUEsa0JBQWtCc0MsSUFBSSxLQUFLLGFBQWE7WUFDMUMsTUFBTWIsUUFBUXVELE9BQU9uRSxDQUFDLEdBQUdmLFdBQVdlLENBQUM7WUFDckMsTUFBTWEsU0FBU3NELE9BQU9sRSxDQUFDLEdBQUdoQixXQUFXZ0IsQ0FBQztZQUN0Q2IscUJBQXFCO2dCQUNuQixHQUFHRCxpQkFBaUI7Z0JBQ3BCdUMsYUFBYTtvQkFBQ3pDLFdBQVdlLENBQUM7b0JBQUVmLFdBQVdnQixDQUFDO29CQUFFVztvQkFBT0M7aUJBQU87WUFDMUQ7UUFDRixPQUFPLElBQUkxQixrQkFBa0JzQyxJQUFJLEtBQUssVUFBVTtZQUM5QyxNQUFNTSxTQUFTRyxLQUFLcUMsSUFBSSxDQUN0QixDQUFDSixPQUFPbkUsQ0FBQyxHQUFHZixXQUFXZSxDQUFDLEtBQUssSUFBSSxDQUFDbUUsT0FBT2xFLENBQUMsR0FBR2hCLFdBQVdnQixDQUFDLEtBQUs7WUFFaEViLHFCQUFxQjtnQkFDbkIsR0FBR0QsaUJBQWlCO2dCQUNwQnVDLGFBQWE7b0JBQUN6QyxXQUFXZSxDQUFDO29CQUFFZixXQUFXZ0IsQ0FBQztvQkFBRThCO2lCQUFPO1lBQ25EO1FBQ0Y7SUFDRjtJQUVBLE1BQU0rQyxnQkFBZ0I7UUFDcEIsSUFBSS9GLGFBQWFJLG1CQUFtQjtZQUNsQyxtREFBbUQ7WUFDbkQsTUFBTTRGLG9CQUNKLGtCQUFtQnRELElBQUksS0FBSyxlQUMzQlMsS0FBSzhDLEdBQUcsQ0FBQzdGLGtCQUFrQnVDLFdBQVcsQ0FBQyxFQUFFLElBQUksS0FDN0NRLEtBQUs4QyxHQUFHLENBQUM3RixrQkFBa0J1QyxXQUFXLENBQUMsRUFBRSxJQUFJLEtBQzdDdkMsa0JBQWtCc0MsSUFBSSxLQUFLLFlBQzNCdEMsa0JBQWtCdUMsV0FBVyxDQUFDLEVBQUUsR0FBRztZQUV0QyxJQUFJcUQsbUJBQW1CO2dCQUNyQnRHLG1CQUFtQlU7WUFDckI7UUFDRjtRQUVBSCxhQUFhO1FBQ2JFLGNBQWM7UUFDZEUscUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTTZGLG1CQUFtQjtRQUN2QnpGLGlCQUFpQjtRQUNqQnNGO0lBQ0Y7SUFFQSxNQUFNSSxvQkFBb0IsQ0FBQzNCO1FBQ3pCLE1BQU1ZLFNBQVNOLHFCQUFxQk47UUFFcEMsaURBQWlEO1FBQ2pELE1BQU1hLG9CQUFvQm5HLFlBQVlvRyxJQUFJLENBQUNsRCxDQUFBQTtZQUN6QyxNQUFNdUIsU0FBU3ZCLFdBQVdPLFdBQVcsQ0FBQyxFQUFFO1lBQ3hDLE1BQU1pQixTQUFTeEIsV0FBV08sV0FBVyxDQUFDLEVBQUUsR0FBRztZQUMzQyxNQUFNZixTQUFTOUIsVUFBVWtDLE9BQU87WUFDaEMsSUFBSSxDQUFDSixRQUFRLE9BQU87WUFFcEIsTUFBTUYsTUFBTUUsT0FBTzBDLFVBQVUsQ0FBQztZQUM5QixJQUFJLENBQUM1QyxLQUFLLE9BQU87WUFFakJBLElBQUlnQyxJQUFJLEdBQUc7WUFDWCxNQUFNSyxZQUFZckMsSUFBSXNDLFdBQVcsQ0FBQzVCLFdBQVdxQixLQUFLLEVBQUU1QixLQUFLO1lBRXpELE9BQU91RCxPQUFPbkUsQ0FBQyxJQUFJMEMsU0FBUyxLQUFLeUIsT0FBT25FLENBQUMsSUFBSTBDLFNBQVNJLFlBQVksS0FDM0RxQixPQUFPbEUsQ0FBQyxJQUFJMEMsU0FBUyxNQUFNd0IsT0FBT2xFLENBQUMsSUFBSTBDLFNBQVM7UUFDekQ7UUFFQSxJQUFJeUIsbUJBQW1CO1lBQ3JCMUUsZ0JBQWdCMEUsa0JBQWtCNUMsRUFBRTtZQUNwQzVCLHFCQUFxQndFLGtCQUFrQjVCLEtBQUs7UUFDOUM7SUFDRjtJQUVBLE1BQU0yQyxvQkFBb0IsQ0FBQzVCO1lBSVoxRTtRQUhiMEUsRUFBRTZCLGNBQWM7UUFFaEIsTUFBTWpCLFNBQVNOLHFCQUFxQk47UUFDcEMsTUFBTU8sUUFBT2pGLHFCQUFBQSxVQUFVa0MsT0FBTyxjQUFqQmxDLHlDQUFBQSxtQkFBbUJrRixxQkFBcUI7UUFDckQsSUFBSSxDQUFDRCxNQUFNO1FBRVgsb0RBQW9EO1FBQ3BELE1BQU1NLG9CQUFvQm5HLFlBQVlvRyxJQUFJLENBQUNsRCxDQUFBQTtZQUN6QyxPQUFRQSxXQUFXTSxJQUFJO2dCQUNyQixLQUFLO29CQUNILE1BQU0sQ0FBQ3pCLEdBQUdDLEdBQUdXLE9BQU9DLE9BQU8sR0FBR00sV0FBV08sV0FBVztvQkFDcEQsT0FBT3lDLE9BQU9uRSxDQUFDLElBQUlBLEtBQUttRSxPQUFPbkUsQ0FBQyxJQUFJQSxJQUFJWSxTQUNqQ3VELE9BQU9sRSxDQUFDLElBQUlBLEtBQUtrRSxPQUFPbEUsQ0FBQyxJQUFJQSxJQUFJWTtnQkFDMUMsS0FBSztvQkFDSCxNQUFNLENBQUNnQixJQUFJQyxJQUFJQyxPQUFPLEdBQUdaLFdBQVdPLFdBQVc7b0JBQy9DLE1BQU00QyxXQUFXcEMsS0FBS3FDLElBQUksQ0FBQyxDQUFDSixPQUFPbkUsQ0FBQyxHQUFHNkIsRUFBQyxLQUFNLElBQUksQ0FBQ3NDLE9BQU9sRSxDQUFDLEdBQUc2QixFQUFDLEtBQU07b0JBQ3JFLE9BQU93QyxZQUFZdkM7Z0JBQ3JCLEtBQUs7b0JBQ0gsTUFBTSxDQUFDTyxJQUFJQyxHQUFHLEdBQUdwQixXQUFXTyxXQUFXO29CQUN2QyxNQUFNOEMsZ0JBQWdCdEMsS0FBS3FDLElBQUksQ0FBQyxDQUFDSixPQUFPbkUsQ0FBQyxHQUFHc0MsRUFBQyxLQUFNLElBQUksQ0FBQzZCLE9BQU9sRSxDQUFDLEdBQUdzQyxFQUFDLEtBQU07b0JBQzFFLE9BQU9pQyxpQkFBaUI7Z0JBQzFCO29CQUNFLE9BQU87WUFDWDtRQUNGO1FBRUExRSxlQUFlO1lBQ2JDLE1BQU07WUFDTkMsR0FBR3VELEVBQUVDLE9BQU87WUFDWnZELEdBQUdzRCxFQUFFRSxPQUFPO1lBQ1p2RCxrQkFBa0JrRSxxQkFBcUI7WUFDdkNqRSxlQUFlZ0U7UUFDakI7SUFDRjtJQUVBLE1BQU1rQixrQkFBa0I7UUFDdEJ2RixlQUFld0YsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFdkYsTUFBTTtZQUFNO0lBQ2pEO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU13RixrQkFBa0I7UUFDdEIsTUFBTUMsWUFBWSxJQUFJLG1CQUFtQjs7UUFDekMsTUFBTUMsYUFBYSxJQUFJLHdCQUF3Qjs7UUFDL0MsTUFBTUMsU0FBUztRQUVmLElBQUkxRixJQUFJSSxvQkFBb0JKLENBQUMsR0FBRzBGO1FBQ2hDLElBQUl6RixJQUFJRyxvQkFBb0JILENBQUMsR0FBR3lGO1FBRWhDLHVCQUF1QjtRQUN2QixJQUFJMUYsSUFBSXdGLFlBQVlHLE9BQU9DLFVBQVUsRUFBRTtZQUNyQzVGLElBQUlJLG9CQUFvQkosQ0FBQyxHQUFHd0YsWUFBWUU7UUFDMUM7UUFFQSx3QkFBd0I7UUFDeEIsSUFBSXpGLElBQUl3RixhQUFhRSxPQUFPRSxXQUFXLEVBQUU7WUFDdkM1RixJQUFJRyxvQkFBb0JILENBQUMsR0FBR3dGLGFBQWFDO1FBQzNDO1FBRUEsb0NBQW9DO1FBQ3BDMUYsSUFBSWtDLEtBQUs0RCxHQUFHLENBQUMsR0FBRzVELEtBQUs2RCxHQUFHLENBQUMvRixHQUFHMkYsT0FBT0MsVUFBVSxHQUFHSjtRQUNoRHZGLElBQUlpQyxLQUFLNEQsR0FBRyxDQUFDLEdBQUc1RCxLQUFLNkQsR0FBRyxDQUFDOUYsR0FBRzBGLE9BQU9FLFdBQVcsR0FBR0o7UUFFakQsT0FBTztZQUFFekY7WUFBR0M7UUFBRTtJQUNoQjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNK0YseUJBQXlCO1FBQzdCLElBQUluRyxZQUFZSyxnQkFBZ0IsRUFBRTtZQUNoQ3RCLG1CQUFtQmlCLFlBQVlLLGdCQUFnQixDQUFDc0IsRUFBRTtRQUNwRDtRQUNBNkQ7SUFDRjtJQUVBLE1BQU1ZLGtCQUFrQjtRQUN0QixJQUFJcEcsWUFBWUssZ0JBQWdCLEVBQUU7WUFDaENSLGdCQUFnQkcsWUFBWUssZ0JBQWdCLENBQUNzQixFQUFFO1lBQy9DNUIscUJBQXFCQyxZQUFZSyxnQkFBZ0IsQ0FBQ3NDLEtBQUs7UUFDekQ7UUFDQTZDO0lBQ0Y7SUFFQSxNQUFNYSxzQkFBc0IsQ0FBQ3pFO1FBQzNCLElBQUksQ0FBQzVCLFlBQVlNLGFBQWEsRUFBRTtRQUVoQyxNQUFNZ0UsU0FBU3RFLFlBQVlNLGFBQWE7UUFDeEMsSUFBSXNFO1FBRUosSUFBSWhELFNBQVMsU0FBUztZQUNwQmdELGdCQUFnQjtnQkFDZGpELElBQUlrRCxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7Z0JBQ3ZCbkQsTUFBTTtnQkFDTkMsYUFBYTtvQkFBQ3lDLE9BQU9uRSxDQUFDO29CQUFFbUUsT0FBT2xFLENBQUM7aUJBQUM7Z0JBQ2pDdUMsT0FBT3BFO2dCQUNQaUQsT0FBT2hEO1lBQ1Q7UUFDRixPQUFPLElBQUlvRCxTQUFTLGFBQWE7WUFDL0IsbUNBQW1DO1lBQ25DZ0QsZ0JBQWdCO2dCQUNkakQsSUFBSWtELEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtnQkFDdkJuRCxNQUFNO2dCQUNOQyxhQUFhO29CQUFDeUMsT0FBT25FLENBQUMsR0FBRztvQkFBSW1FLE9BQU9sRSxDQUFDLEdBQUc7b0JBQUk7b0JBQUk7aUJBQUc7Z0JBQ25EdUMsT0FBT3BFO2dCQUNQaUQsT0FBT2hEO1lBQ1Q7UUFDRixPQUFPLElBQUlvRCxTQUFTLFVBQVU7WUFDNUIsZ0NBQWdDO1lBQ2hDZ0QsZ0JBQWdCO2dCQUNkakQsSUFBSWtELEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtnQkFDdkJuRCxNQUFNO2dCQUNOQyxhQUFhO29CQUFDeUMsT0FBT25FLENBQUM7b0JBQUVtRSxPQUFPbEUsQ0FBQztvQkFBRTtpQkFBRztnQkFDckN1QyxPQUFPcEU7Z0JBQ1BpRCxPQUFPaEQ7WUFDVDtRQUNGLE9BQU87WUFDTDtRQUNGO1FBRUFJLG1CQUFtQmdHO1FBQ25CWTtJQUNGO0lBRUEsTUFBTWMsb0JBQW9CLENBQUNDO1FBQ3pCLElBQUl2RyxZQUFZSyxnQkFBZ0IsRUFBRTtZQUNoQyxNQUFNbUcsY0FBYzdILGlCQUFpQjZGLElBQUksQ0FBQ2lDLENBQUFBLElBQUtBLEVBQUVDLElBQUksS0FBS0g7WUFDMUQxSCxtQkFBbUJtQixZQUFZSyxnQkFBZ0IsQ0FBQ3NCLEVBQUUsRUFBRTtnQkFDbERnQixPQUFPNEQ7Z0JBQ1AvRSxPQUFPZ0YsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhaEYsS0FBSyxLQUFJaEQ7WUFDL0I7UUFDRjtRQUNBZ0g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDbUI7UUFDQ0MsV0FBVTtRQUNWQyxPQUFPO1lBQUU5RixPQUFPN0M7WUFBWThDLFFBQVE3QztRQUFZOztZQUUvQyxDQUFDcUIsNkJBQ0EsOERBQUNtSDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUszQyw4REFBQ0c7Z0JBQ0NDLEtBQUsvSDtnQkFDTGdJLEtBQUtoSjtnQkFDTGlKLEtBQUk7Z0JBQ0pOLFdBQVU7Z0JBQ1ZDLE9BQU87b0JBQUU5RixPQUFPN0M7b0JBQVk4QyxRQUFRN0M7Z0JBQVk7Z0JBQ2hEZ0osUUFBUTtvQkFDTjFHLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJ6QztvQkFDMUN3QixlQUFlO29CQUNmLE1BQU1xQixTQUFTOUIsVUFBVWtDLE9BQU87b0JBQ2hDLElBQUlKLFFBQVE7d0JBQ1YsTUFBTUYsTUFBTUUsT0FBTzBDLFVBQVUsQ0FBQzt3QkFDOUIsSUFBSTVDLEtBQUtELGdCQUFnQkM7b0JBQzNCO2dCQUNGO2dCQUNBd0csU0FBUyxDQUFDMUQ7b0JBQ1JqRCxRQUFRNEcsS0FBSyxDQUFDLHlCQUF5QnBKLFVBQVV5RjtvQkFDakRqRSxlQUFlO2dCQUNqQjs7Ozs7OzBCQUVGLDhEQUFDcUI7Z0JBQ0NrRyxLQUFLaEk7Z0JBQ0w0SCxXQUFVO2dCQUNWQyxPQUFPO29CQUFFOUYsT0FBTzdDO29CQUFZOEMsUUFBUTdDO2dCQUFZO2dCQUNoRG1KLGFBQWFqRDtnQkFDYmtELGFBQWF2QztnQkFDYndDLFdBQVd2QztnQkFDWHdDLGNBQWNyQztnQkFDZHNDLGVBQWVyQztnQkFDZnNDLGVBQWVyQztnQkFDZnNDLFNBQVNwQzs7Ozs7O1lBSVY1Riw4QkFDQyw4REFBQytHO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDaUI7b0JBQ0NqRyxNQUFLO29CQUNMa0csT0FBT2hJO29CQUNQaUksVUFBVSxDQUFDckUsSUFBTTNELHFCQUFxQjJELEVBQUVzRSxNQUFNLENBQUNGLEtBQUs7b0JBQ3BERyxRQUFRO3dCQUNOLElBQUluSSxrQkFBa0JvSSxJQUFJLElBQUk7NEJBQzVCckosbUJBQW1CZSxjQUFjO2dDQUFFK0MsT0FBTzdDLGtCQUFrQm9JLElBQUk7NEJBQUc7d0JBQ3JFO3dCQUNBckksZ0JBQWdCO3dCQUNoQkUscUJBQXFCO29CQUN2QjtvQkFDQW9JLFdBQVcsQ0FBQ3pFO3dCQUNWLElBQUlBLEVBQUUwRSxHQUFHLEtBQUssU0FBUzs0QkFDckIsSUFBSXRJLGtCQUFrQm9JLElBQUksSUFBSTtnQ0FDNUJySixtQkFBbUJlLGNBQWM7b0NBQUUrQyxPQUFPN0Msa0JBQWtCb0ksSUFBSTtnQ0FBRzs0QkFDckU7NEJBQ0FySSxnQkFBZ0I7NEJBQ2hCRSxxQkFBcUI7d0JBQ3ZCLE9BQU8sSUFBSTJELEVBQUUwRSxHQUFHLEtBQUssVUFBVTs0QkFDN0J2SSxnQkFBZ0I7NEJBQ2hCRSxxQkFBcUI7d0JBQ3ZCO29CQUNGO29CQUNBNkcsV0FBVTtvQkFDVkMsT0FBTzt3QkFDTHdCLFVBQVU7d0JBQ1ZsRSxNQUFNL0YsRUFBQUEsb0JBQUFBLFlBQVlvRyxJQUFJLENBQUM4RCxDQUFBQSxJQUFLQSxFQUFFM0csRUFBRSxLQUFLL0IsMkJBQS9CeEIsd0NBQUFBLGtCQUE4Q3lELFdBQVcsQ0FBQyxFQUFFLEtBQUk7d0JBQ3RFdUMsS0FBSyxDQUFDaEcsRUFBQUEscUJBQUFBLFlBQVlvRyxJQUFJLENBQUM4RCxDQUFBQSxJQUFLQSxFQUFFM0csRUFBRSxLQUFLL0IsMkJBQS9CeEIseUNBQUFBLG1CQUE4Q3lELFdBQVcsQ0FBQyxFQUFFLEtBQUksS0FBSzt3QkFDM0UwRyxRQUFRO29CQUNWO29CQUNBQyxTQUFTOzs7Ozs7Ozs7OztZQU1keEksWUFBWUUsSUFBSSxrQkFDZjs7a0NBRUUsOERBQUN5Rzt3QkFDQ0MsV0FBVTt3QkFDVmdCLFNBQVNwQzs7Ozs7O2tDQUlYLDhEQUFDbUI7d0JBQ0NDLFdBQVU7d0JBQ1ZDLE9BQU87NEJBQ0wxQyxNQUFNNUQsb0JBQW9CSixDQUFDLEdBQUc7NEJBQzlCaUUsS0FBSzdELG9CQUFvQkgsQ0FBQyxHQUFHOzRCQUM3QnFJLFdBQVc7d0JBQ2I7a0NBRUN6SSxZQUFZSyxnQkFBZ0IsR0FDM0IsK0JBQStCO3NDQUMvQjs7OENBQ0UsOERBQUNzRztvQ0FBSUMsV0FBVTs7d0NBQ1o1RyxZQUFZSyxnQkFBZ0IsQ0FBQ3NDLEtBQUs7d0NBQUM7d0NBQUczQyxZQUFZSyxnQkFBZ0IsQ0FBQ3VCLElBQUk7d0NBQUM7Ozs7Ozs7OENBRzNFLDhEQUFDOEc7b0NBQ0M5QixXQUFVO29DQUNWZ0IsU0FBU3hCOztzREFFVCw4REFBQ3VDOzRDQUFLL0IsV0FBVTtzREFBTzs7Ozs7O3dDQUFTOzs7Ozs7OzhDQUlsQyw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWtDOzs7Ozs7Z0NBQ2hEakksaUJBQWlCaUssR0FBRyxDQUFDLENBQUNqRyxzQkFDckIsOERBQUMrRjt3Q0FFQzlCLFdBQVU7d0NBQ1ZnQixTQUFTLElBQU10QixrQkFBa0IzRCxNQUFNK0QsSUFBSTs7MERBRTNDLDhEQUFDQztnREFDQ0MsV0FBVTtnREFDVkMsT0FBTztvREFBRWdDLGlCQUFpQmxHLE1BQU1uQixLQUFLO2dEQUFDOzs7Ozs7NENBRXZDbUIsTUFBTStELElBQUk7O3VDQVJOL0QsTUFBTStELElBQUk7Ozs7OzhDQVluQiw4REFBQ0M7b0NBQUlDLFdBQVU7Ozs7Ozs4Q0FFZiw4REFBQzhCO29DQUNDOUIsV0FBVTtvQ0FDVmdCLFNBQVN6Qjs7c0RBRVQsOERBQUN3Qzs0Q0FBSy9CLFdBQVU7c0RBQU87Ozs7Ozt3Q0FBVTs7Ozs7Ozs7MkNBS3JDLHNCQUFzQjtzQ0FDdEI7OzhDQUNFLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBdUQ7Ozs7Ozs4Q0FJdEUsOERBQUM4QjtvQ0FDQzlCLFdBQVU7b0NBQ1ZnQixTQUFTLElBQU12QixvQkFBb0I7O3NEQUVuQyw4REFBQ3NDOzRDQUFLL0IsV0FBVTtzREFBTzs7Ozs7O3dDQUFTOzs7Ozs7OzhDQUlsQyw4REFBQzhCO29DQUNDOUIsV0FBVTtvQ0FDVmdCLFNBQVMsSUFBTXZCLG9CQUFvQjs7c0RBRW5DLDhEQUFDc0M7NENBQUsvQixXQUFVO3NEQUFPOzs7Ozs7d0NBQVE7Ozs7Ozs7OENBSWpDLDhEQUFDOEI7b0NBQ0M5QixXQUFVO29DQUNWZ0IsU0FBUyxJQUFNdkIsb0JBQW9COztzREFFbkMsOERBQUNzQzs0Q0FBSy9CLFdBQVU7c0RBQU87Ozs7Ozt3Q0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVqRDtHQXJwQmdCNUk7S0FBQUEiLCJzb3VyY2VzIjpbIkY6XFxkZWVwc2lnaHRcXGZyb25ldGVuZFxcY29tcG9uZW50c1xcYW5ub3RhdGlvblxcaW1hZ2UtYW5ub3RhdGlvbi1jYW52YXMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VSZWYsIHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5cbmV4cG9ydCBpbnRlcmZhY2UgQW5ub3RhdGlvblNoYXBlIHtcbiAgaWQ6IHN0cmluZ1xuICB0eXBlOiAncmVjdGFuZ2xlJyB8ICdjaXJjbGUnIHwgJ3BvaW50J1xuICBjb29yZGluYXRlczogbnVtYmVyW11cbiAgbGFiZWw6IHN0cmluZ1xuICBjb2xvcjogc3RyaW5nXG4gIGNvbmZpZGVuY2U/OiBudW1iZXJcbn1cblxuaW50ZXJmYWNlIEltYWdlQW5ub3RhdGlvbkNhbnZhc1Byb3BzIHtcbiAgaW1hZ2VVcmw6IHN0cmluZ1xuICBpbWFnZVdpZHRoOiBudW1iZXJcbiAgaW1hZ2VIZWlnaHQ6IG51bWJlclxuICBhbm5vdGF0aW9uczogQW5ub3RhdGlvblNoYXBlW11cbiAgc2VsZWN0ZWRBbm5vdGF0aW9uPzogc3RyaW5nIHwgbnVsbFxuICBjdXJyZW50VG9vbDogJ3NlbGVjdCcgfCAncmVjdGFuZ2xlJyB8ICdjaXJjbGUnIHwgJ3BvaW50J1xuICBjdXJyZW50TGFiZWw6IHN0cmluZ1xuICBjdXJyZW50Q29sb3I6IHN0cmluZ1xuICB6b29tOiBudW1iZXJcbiAgc2hvd0xhYmVsczogYm9vbGVhblxuICBwcmVkZWZpbmVkTGFiZWxzOiBBcnJheTx7IG5hbWU6IHN0cmluZzsgY29sb3I6IHN0cmluZyB9PlxuICBvbkFubm90YXRpb25DcmVhdGU6IChhbm5vdGF0aW9uOiBBbm5vdGF0aW9uU2hhcGUpID0+IHZvaWRcbiAgb25Bbm5vdGF0aW9uVXBkYXRlOiAoaWQ6IHN0cmluZywgYW5ub3RhdGlvbjogUGFydGlhbDxBbm5vdGF0aW9uU2hhcGU+KSA9PiB2b2lkXG4gIG9uQW5ub3RhdGlvblNlbGVjdDogKGlkOiBzdHJpbmcgfCBudWxsKSA9PiB2b2lkXG4gIG9uQW5ub3RhdGlvbkRlbGV0ZTogKGlkOiBzdHJpbmcpID0+IHZvaWRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEltYWdlQW5ub3RhdGlvbkNhbnZhcyh7XG4gIGltYWdlVXJsLFxuICBpbWFnZVdpZHRoLFxuICBpbWFnZUhlaWdodCxcbiAgYW5ub3RhdGlvbnMsXG4gIHNlbGVjdGVkQW5ub3RhdGlvbixcbiAgY3VycmVudFRvb2wsXG4gIGN1cnJlbnRMYWJlbCxcbiAgY3VycmVudENvbG9yLFxuICB6b29tLFxuICBzaG93TGFiZWxzLFxuICBwcmVkZWZpbmVkTGFiZWxzLFxuICBvbkFubm90YXRpb25DcmVhdGUsXG4gIG9uQW5ub3RhdGlvblVwZGF0ZSxcbiAgb25Bbm5vdGF0aW9uU2VsZWN0LFxuICBvbkFubm90YXRpb25EZWxldGUsXG59OiBJbWFnZUFubm90YXRpb25DYW52YXNQcm9wcykge1xuICBjb25zdCBjYW52YXNSZWYgPSB1c2VSZWY8SFRNTENhbnZhc0VsZW1lbnQ+KG51bGwpXG4gIGNvbnN0IGltYWdlUmVmID0gdXNlUmVmPEhUTUxJbWFnZUVsZW1lbnQ+KG51bGwpXG4gIGNvbnN0IFtpc0RyYXdpbmcsIHNldElzRHJhd2luZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3N0YXJ0UG9pbnQsIHNldFN0YXJ0UG9pbnRdID0gdXNlU3RhdGU8eyB4OiBudW1iZXI7IHk6IG51bWJlciB9IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2N1cnJlbnRBbm5vdGF0aW9uLCBzZXRDdXJyZW50QW5ub3RhdGlvbl0gPSB1c2VTdGF0ZTxBbm5vdGF0aW9uU2hhcGUgfCBudWxsPihudWxsKVxuICBjb25zdCBbaW1hZ2VMb2FkZWQsIHNldEltYWdlTG9hZGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbbW91c2VQb3NpdGlvbiwgc2V0TW91c2VQb3NpdGlvbl0gPSB1c2VTdGF0ZTx7IHg6IG51bWJlcjsgeTogbnVtYmVyIH0gfCBudWxsPihudWxsKVxuICBjb25zdCBbZWRpdGluZ0xhYmVsLCBzZXRFZGl0aW5nTGFiZWxdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2VkaXRpbmdMYWJlbFZhbHVlLCBzZXRFZGl0aW5nTGFiZWxWYWx1ZV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2NvbnRleHRNZW51LCBzZXRDb250ZXh0TWVudV0gPSB1c2VTdGF0ZTx7XG4gICAgc2hvdzogYm9vbGVhblxuICAgIHg6IG51bWJlclxuICAgIHk6IG51bWJlclxuICAgIHRhcmdldEFubm90YXRpb246IEFubm90YXRpb25TaGFwZSB8IG51bGxcbiAgICBjbGlja1Bvc2l0aW9uOiB7IHg6IG51bWJlcjsgeTogbnVtYmVyIH0gfCBudWxsXG4gIH0+KHtcbiAgICBzaG93OiBmYWxzZSxcbiAgICB4OiAwLFxuICAgIHk6IDAsXG4gICAgdGFyZ2V0QW5ub3RhdGlvbjogbnVsbCxcbiAgICBjbGlja1Bvc2l0aW9uOiBudWxsXG4gIH0pXG4gIGNvbnN0IFtnbG9iYWxNb3VzZVBvc2l0aW9uLCBzZXRHbG9iYWxNb3VzZVBvc2l0aW9uXSA9IHVzZVN0YXRlPHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfT4oeyB4OiAwLCB5OiAwIH0pXG5cbiAgLy8gRGVidWcgbG9nZ2luZ1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdJbWFnZUFubm90YXRpb25DYW52YXMgcHJvcHM6Jywge1xuICAgICAgaW1hZ2VVcmwsXG4gICAgICBpbWFnZVdpZHRoLFxuICAgICAgaW1hZ2VIZWlnaHQsXG4gICAgICBpbWFnZUxvYWRlZFxuICAgIH0pXG4gIH0sIFtpbWFnZVVybCwgaW1hZ2VXaWR0aCwgaW1hZ2VIZWlnaHQsIGltYWdlTG9hZGVkXSlcblxuICBjb25zdCBkcmF3QW5ub3RhdGlvbnMgPSB1c2VDYWxsYmFjaygoY3R4OiBDYW52YXNSZW5kZXJpbmdDb250ZXh0MkQpID0+IHtcbiAgICBjdHguY2xlYXJSZWN0KDAsIDAsIGN0eC5jYW52YXMud2lkdGgsIGN0eC5jYW52YXMuaGVpZ2h0KVxuICAgIFxuICAgIC8vIERyYXcgaW1hZ2VcbiAgICBjb25zdCBpbWFnZSA9IGltYWdlUmVmLmN1cnJlbnRcbiAgICBpZiAoaW1hZ2UgJiYgaW1hZ2UuY29tcGxldGUpIHtcbiAgICAgIGN0eC5kcmF3SW1hZ2UoaW1hZ2UsIDAsIDAsIGltYWdlV2lkdGgsIGltYWdlSGVpZ2h0KVxuICAgIH1cblxuICAgIC8vIERyYXcgYW5ub3RhdGlvbnNcbiAgICBhbm5vdGF0aW9ucy5mb3JFYWNoKChhbm5vdGF0aW9uKSA9PiB7XG4gICAgICBjdHguc3Ryb2tlU3R5bGUgPSBhbm5vdGF0aW9uLmNvbG9yXG4gICAgICBjdHguZmlsbFN0eWxlID0gYW5ub3RhdGlvbi5jb2xvciArICczMCcgLy8gMzAlIG9wYWNpdHlcbiAgICAgIGN0eC5saW5lV2lkdGggPSBhbm5vdGF0aW9uLmlkID09PSBzZWxlY3RlZEFubm90YXRpb24gPyAzIDogMlxuXG4gICAgICBzd2l0Y2ggKGFubm90YXRpb24udHlwZSkge1xuICAgICAgICBjYXNlICdyZWN0YW5nbGUnOlxuICAgICAgICAgIGNvbnN0IFt4LCB5LCB3aWR0aCwgaGVpZ2h0XSA9IGFubm90YXRpb24uY29vcmRpbmF0ZXNcbiAgICAgICAgICBjdHguc3Ryb2tlUmVjdCh4LCB5LCB3aWR0aCwgaGVpZ2h0KVxuICAgICAgICAgIGN0eC5maWxsUmVjdCh4LCB5LCB3aWR0aCwgaGVpZ2h0KVxuICAgICAgICAgIGJyZWFrXG5cbiAgICAgICAgY2FzZSAnY2lyY2xlJzpcbiAgICAgICAgICBjb25zdCBbY3gsIGN5LCByYWRpdXNdID0gYW5ub3RhdGlvbi5jb29yZGluYXRlc1xuICAgICAgICAgIGN0eC5iZWdpblBhdGgoKVxuICAgICAgICAgIGN0eC5hcmMoY3gsIGN5LCByYWRpdXMsIDAsIDIgKiBNYXRoLlBJKVxuICAgICAgICAgIGN0eC5zdHJva2UoKVxuICAgICAgICAgIGN0eC5maWxsKClcbiAgICAgICAgICBicmVha1xuXG4gICAgICAgIGNhc2UgJ3BvaW50JzpcbiAgICAgICAgICBjb25zdCBbcHgsIHB5XSA9IGFubm90YXRpb24uY29vcmRpbmF0ZXNcbiAgICAgICAgICBjdHguYmVnaW5QYXRoKClcbiAgICAgICAgICBjdHguYXJjKHB4LCBweSwgNSwgMCwgMiAqIE1hdGguUEkpXG4gICAgICAgICAgY3R4LmZpbGwoKVxuICAgICAgICAgIGJyZWFrXG5cblxuICAgICAgfVxuXG4gICAgICAvLyBEcmF3IGxhYmVsXG4gICAgICBpZiAoc2hvd0xhYmVscyAmJiBhbm5vdGF0aW9uLmxhYmVsKSB7XG4gICAgICAgIGN0eC5maWxsU3R5bGUgPSBhbm5vdGF0aW9uLmNvbG9yXG4gICAgICAgIGN0eC5mb250ID0gJ2JvbGQgMTRweCBBcmlhbCdcbiAgICAgICAgY3R4LnN0cm9rZVN0eWxlID0gJ3doaXRlJ1xuICAgICAgICBjdHgubGluZVdpZHRoID0gM1xuXG4gICAgICAgIGNvbnN0IGxhYmVsWCA9IGFubm90YXRpb24uY29vcmRpbmF0ZXNbMF1cbiAgICAgICAgY29uc3QgbGFiZWxZID0gYW5ub3RhdGlvbi5jb29yZGluYXRlc1sxXSAtIDhcblxuICAgICAgICAvLyBEcmF3IHRleHQgb3V0bGluZVxuICAgICAgICBjdHguc3Ryb2tlVGV4dChhbm5vdGF0aW9uLmxhYmVsLCBsYWJlbFgsIGxhYmVsWSlcbiAgICAgICAgLy8gRHJhdyB0ZXh0XG4gICAgICAgIGN0eC5maWxsVGV4dChhbm5vdGF0aW9uLmxhYmVsLCBsYWJlbFgsIGxhYmVsWSlcblxuICAgICAgICAvLyBEcmF3IGVkaXQgaW5kaWNhdG9yIGZvciBzZWxlY3RlZCBhbm5vdGF0aW9uXG4gICAgICAgIGlmIChhbm5vdGF0aW9uLmlkID09PSBzZWxlY3RlZEFubm90YXRpb24pIHtcbiAgICAgICAgICBjdHguZmlsbFN0eWxlID0gJ3JnYmEoNTksIDEzMCwgMjQ2LCAwLjIpJ1xuICAgICAgICAgIGN0eC5zdHJva2VTdHlsZSA9ICcjM2I4MmY2J1xuICAgICAgICAgIGN0eC5saW5lV2lkdGggPSAxXG4gICAgICAgICAgY29uc3QgdGV4dFdpZHRoID0gY3R4Lm1lYXN1cmVUZXh0KGFubm90YXRpb24ubGFiZWwpLndpZHRoXG4gICAgICAgICAgY3R4LmZpbGxSZWN0KGxhYmVsWCAtIDIsIGxhYmVsWSAtIDE2LCB0ZXh0V2lkdGggKyA0LCAyMClcbiAgICAgICAgICBjdHguc3Ryb2tlUmVjdChsYWJlbFggLSAyLCBsYWJlbFkgLSAxNiwgdGV4dFdpZHRoICsgNCwgMjApXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KVxuXG4gICAgLy8gRHJhdyBjdXJyZW50IGFubm90YXRpb24gYmVpbmcgY3JlYXRlZFxuICAgIGlmIChjdXJyZW50QW5ub3RhdGlvbikge1xuICAgICAgY3R4LnN0cm9rZVN0eWxlID0gY3VycmVudENvbG9yXG4gICAgICBjdHguZmlsbFN0eWxlID0gY3VycmVudENvbG9yICsgJzMwJ1xuICAgICAgY3R4LmxpbmVXaWR0aCA9IDJcbiAgICAgIGN0eC5zZXRMaW5lRGFzaChbNSwgNV0pXG5cbiAgICAgIHN3aXRjaCAoY3VycmVudEFubm90YXRpb24udHlwZSkge1xuICAgICAgICBjYXNlICdyZWN0YW5nbGUnOlxuICAgICAgICAgIGNvbnN0IFt4LCB5LCB3aWR0aCwgaGVpZ2h0XSA9IGN1cnJlbnRBbm5vdGF0aW9uLmNvb3JkaW5hdGVzXG4gICAgICAgICAgY3R4LnN0cm9rZVJlY3QoeCwgeSwgd2lkdGgsIGhlaWdodClcbiAgICAgICAgICBicmVha1xuXG4gICAgICAgIGNhc2UgJ2NpcmNsZSc6XG4gICAgICAgICAgY29uc3QgW2N4LCBjeSwgcmFkaXVzXSA9IGN1cnJlbnRBbm5vdGF0aW9uLmNvb3JkaW5hdGVzXG4gICAgICAgICAgY3R4LmJlZ2luUGF0aCgpXG4gICAgICAgICAgY3R4LmFyYyhjeCwgY3ksIHJhZGl1cywgMCwgMiAqIE1hdGguUEkpXG4gICAgICAgICAgY3R4LnN0cm9rZSgpXG4gICAgICAgICAgYnJlYWtcbiAgICAgIH1cblxuICAgICAgY3R4LnNldExpbmVEYXNoKFtdKVxuICAgIH1cblxuICAgIC8vIERyYXcgbW91c2UgY29vcmRpbmF0ZXNcbiAgICBpZiAobW91c2VQb3NpdGlvbikge1xuICAgICAgY3R4LmZpbGxTdHlsZSA9ICdyZ2JhKDAsIDAsIDAsIDAuOCknXG4gICAgICBjdHguZm9udCA9ICcxMnB4IEFyaWFsJ1xuICAgICAgY29uc3QgY29vcmRUZXh0ID0gYCgke01hdGgucm91bmQobW91c2VQb3NpdGlvbi54KX0sICR7TWF0aC5yb3VuZChtb3VzZVBvc2l0aW9uLnkpfSlgXG4gICAgICBjb25zdCB0ZXh0V2lkdGggPSBjdHgubWVhc3VyZVRleHQoY29vcmRUZXh0KS53aWR0aFxuXG4gICAgICAvLyBQb3NpdGlvbiB0aGUgY29vcmRpbmF0ZSBkaXNwbGF5IG5lYXIgdGhlIG1vdXNlIGJ1dCB3aXRoaW4gY2FudmFzIGJvdW5kc1xuICAgICAgbGV0IGRpc3BsYXlYID0gbW91c2VQb3NpdGlvbi54ICsgMTBcbiAgICAgIGxldCBkaXNwbGF5WSA9IG1vdXNlUG9zaXRpb24ueSAtIDEwXG5cbiAgICAgIC8vIEFkanVzdCBwb3NpdGlvbiBpZiBpdCB3b3VsZCBnbyBvdXRzaWRlIGNhbnZhc1xuICAgICAgaWYgKGRpc3BsYXlYICsgdGV4dFdpZHRoICsgMTAgPiBpbWFnZVdpZHRoKSB7XG4gICAgICAgIGRpc3BsYXlYID0gbW91c2VQb3NpdGlvbi54IC0gdGV4dFdpZHRoIC0gMTBcbiAgICAgIH1cbiAgICAgIGlmIChkaXNwbGF5WSA8IDIwKSB7XG4gICAgICAgIGRpc3BsYXlZID0gbW91c2VQb3NpdGlvbi55ICsgMjVcbiAgICAgIH1cblxuICAgICAgLy8gRHJhdyBiYWNrZ3JvdW5kXG4gICAgICBjdHguZmlsbFN0eWxlID0gJ3JnYmEoMCwgMCwgMCwgMC44KSdcbiAgICAgIGN0eC5maWxsUmVjdChkaXNwbGF5WCAtIDUsIGRpc3BsYXlZIC0gMTUsIHRleHRXaWR0aCArIDEwLCAyMClcblxuICAgICAgLy8gRHJhdyB0ZXh0XG4gICAgICBjdHguZmlsbFN0eWxlID0gJ3doaXRlJ1xuICAgICAgY3R4LmZpbGxUZXh0KGNvb3JkVGV4dCwgZGlzcGxheVgsIGRpc3BsYXlZKVxuICAgIH1cbiAgfSwgW2Fubm90YXRpb25zLCBzZWxlY3RlZEFubm90YXRpb24sIHNob3dMYWJlbHMsIGN1cnJlbnRBbm5vdGF0aW9uLCBpbWFnZVdpZHRoLCBpbWFnZUhlaWdodCwgY3VycmVudENvbG9yLCBtb3VzZVBvc2l0aW9uXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50XG4gICAgaWYgKCFjYW52YXMpIHJldHVyblxuXG4gICAgY29uc3QgY3R4ID0gY2FudmFzLmdldENvbnRleHQoJzJkJylcbiAgICBpZiAoIWN0eCkgcmV0dXJuXG5cbiAgICBjYW52YXMud2lkdGggPSBpbWFnZVdpZHRoXG4gICAgY2FudmFzLmhlaWdodCA9IGltYWdlSGVpZ2h0XG5cbiAgICBkcmF3QW5ub3RhdGlvbnMoY3R4KVxuICB9LCBbZHJhd0Fubm90YXRpb25zXSlcblxuICAvLyBHbG9iYWwgbW91c2UgdHJhY2tpbmcgZm9yIGNvbnRleHQgbWVudSBmb2xsb3dpbmdcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVHbG9iYWxNb3VzZU1vdmUgPSAoZTogTW91c2VFdmVudCkgPT4ge1xuICAgICAgc2V0R2xvYmFsTW91c2VQb3NpdGlvbih7IHg6IGUuY2xpZW50WCwgeTogZS5jbGllbnRZIH0pXG4gICAgfVxuXG4gICAgaWYgKGNvbnRleHRNZW51LnNob3cpIHtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZUdsb2JhbE1vdXNlTW92ZSlcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZUdsb2JhbE1vdXNlTW92ZSlcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtjb250ZXh0TWVudS5zaG93XSlcblxuICBjb25zdCBnZXRDYW52YXNDb29yZGluYXRlcyA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxDYW52YXNFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50XG4gICAgaWYgKCFjYW52YXMpIHJldHVybiB7IHg6IDAsIHk6IDAgfVxuXG4gICAgY29uc3QgcmVjdCA9IGNhbnZhcy5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKVxuICAgIHJldHVybiB7XG4gICAgICB4OiAoZS5jbGllbnRYIC0gcmVjdC5sZWZ0KSAvIHpvb20sXG4gICAgICB5OiAoZS5jbGllbnRZIC0gcmVjdC50b3ApIC8gem9vbVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZU1vdXNlRG93biA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxDYW52YXNFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGNvb3JkcyA9IGdldENhbnZhc0Nvb3JkaW5hdGVzKGUpXG5cbiAgICBpZiAoY3VycmVudFRvb2wgPT09ICdzZWxlY3QnKSB7XG4gICAgICAvLyBDaGVjayBpZiBjbGlja2luZyBvbiBhbiBleGlzdGluZyBhbm5vdGF0aW9uXG4gICAgICBjb25zdCBjbGlja2VkQW5ub3RhdGlvbiA9IGFubm90YXRpb25zLmZpbmQoYW5ub3RhdGlvbiA9PiB7XG4gICAgICAgIHN3aXRjaCAoYW5ub3RhdGlvbi50eXBlKSB7XG4gICAgICAgICAgY2FzZSAncmVjdGFuZ2xlJzpcbiAgICAgICAgICAgIGNvbnN0IFt4LCB5LCB3aWR0aCwgaGVpZ2h0XSA9IGFubm90YXRpb24uY29vcmRpbmF0ZXNcbiAgICAgICAgICAgIHJldHVybiBjb29yZHMueCA+PSB4ICYmIGNvb3Jkcy54IDw9IHggKyB3aWR0aCAmJiBcbiAgICAgICAgICAgICAgICAgICBjb29yZHMueSA+PSB5ICYmIGNvb3Jkcy55IDw9IHkgKyBoZWlnaHRcbiAgICAgICAgICBjYXNlICdjaXJjbGUnOlxuICAgICAgICAgICAgY29uc3QgW2N4LCBjeSwgcmFkaXVzXSA9IGFubm90YXRpb24uY29vcmRpbmF0ZXNcbiAgICAgICAgICAgIGNvbnN0IGRpc3RhbmNlID0gTWF0aC5zcXJ0KChjb29yZHMueCAtIGN4KSAqKiAyICsgKGNvb3Jkcy55IC0gY3kpICoqIDIpXG4gICAgICAgICAgICByZXR1cm4gZGlzdGFuY2UgPD0gcmFkaXVzXG4gICAgICAgICAgY2FzZSAncG9pbnQnOlxuICAgICAgICAgICAgY29uc3QgW3B4LCBweV0gPSBhbm5vdGF0aW9uLmNvb3JkaW5hdGVzXG4gICAgICAgICAgICBjb25zdCBwb2ludERpc3RhbmNlID0gTWF0aC5zcXJ0KChjb29yZHMueCAtIHB4KSAqKiAyICsgKGNvb3Jkcy55IC0gcHkpICoqIDIpXG4gICAgICAgICAgICByZXR1cm4gcG9pbnREaXN0YW5jZSA8PSAxMFxuICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgICAgfVxuICAgICAgfSlcblxuICAgICAgb25Bbm5vdGF0aW9uU2VsZWN0KGNsaWNrZWRBbm5vdGF0aW9uPy5pZCB8fCBudWxsKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaWYgKGN1cnJlbnRUb29sID09PSAncG9pbnQnKSB7XG4gICAgICBjb25zdCBuZXdBbm5vdGF0aW9uOiBBbm5vdGF0aW9uU2hhcGUgPSB7XG4gICAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICAgIHR5cGU6ICdwb2ludCcsXG4gICAgICAgIGNvb3JkaW5hdGVzOiBbY29vcmRzLngsIGNvb3Jkcy55XSxcbiAgICAgICAgbGFiZWw6IGN1cnJlbnRMYWJlbCxcbiAgICAgICAgY29sb3I6IGN1cnJlbnRDb2xvclxuICAgICAgfVxuICAgICAgb25Bbm5vdGF0aW9uQ3JlYXRlKG5ld0Fubm90YXRpb24pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBzZXRJc0RyYXdpbmcodHJ1ZSlcbiAgICBzZXRTdGFydFBvaW50KGNvb3JkcylcblxuICAgIGlmIChjdXJyZW50VG9vbCA9PT0gJ3JlY3RhbmdsZScpIHtcbiAgICAgIHNldEN1cnJlbnRBbm5vdGF0aW9uKHtcbiAgICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgICAgdHlwZTogJ3JlY3RhbmdsZScsXG4gICAgICAgIGNvb3JkaW5hdGVzOiBbY29vcmRzLngsIGNvb3Jkcy55LCAwLCAwXSxcbiAgICAgICAgbGFiZWw6IGN1cnJlbnRMYWJlbCxcbiAgICAgICAgY29sb3I6IGN1cnJlbnRDb2xvclxuICAgICAgfSlcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRUb29sID09PSAnY2lyY2xlJykge1xuICAgICAgc2V0Q3VycmVudEFubm90YXRpb24oe1xuICAgICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxuICAgICAgICB0eXBlOiAnY2lyY2xlJyxcbiAgICAgICAgY29vcmRpbmF0ZXM6IFtjb29yZHMueCwgY29vcmRzLnksIDBdLFxuICAgICAgICBsYWJlbDogY3VycmVudExhYmVsLFxuICAgICAgICBjb2xvcjogY3VycmVudENvbG9yXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZU1vdXNlTW92ZSA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxDYW52YXNFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGNvb3JkcyA9IGdldENhbnZhc0Nvb3JkaW5hdGVzKGUpXG5cbiAgICAvLyBBbHdheXMgdXBkYXRlIG1vdXNlIHBvc2l0aW9uIGZvciBjb29yZGluYXRlIGRpc3BsYXlcbiAgICBzZXRNb3VzZVBvc2l0aW9uKGNvb3JkcylcblxuICAgIGlmICghaXNEcmF3aW5nIHx8ICFzdGFydFBvaW50IHx8ICFjdXJyZW50QW5ub3RhdGlvbikgcmV0dXJuXG5cbiAgICBpZiAoY3VycmVudEFubm90YXRpb24udHlwZSA9PT0gJ3JlY3RhbmdsZScpIHtcbiAgICAgIGNvbnN0IHdpZHRoID0gY29vcmRzLnggLSBzdGFydFBvaW50LnhcbiAgICAgIGNvbnN0IGhlaWdodCA9IGNvb3Jkcy55IC0gc3RhcnRQb2ludC55XG4gICAgICBzZXRDdXJyZW50QW5ub3RhdGlvbih7XG4gICAgICAgIC4uLmN1cnJlbnRBbm5vdGF0aW9uLFxuICAgICAgICBjb29yZGluYXRlczogW3N0YXJ0UG9pbnQueCwgc3RhcnRQb2ludC55LCB3aWR0aCwgaGVpZ2h0XVxuICAgICAgfSlcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRBbm5vdGF0aW9uLnR5cGUgPT09ICdjaXJjbGUnKSB7XG4gICAgICBjb25zdCByYWRpdXMgPSBNYXRoLnNxcnQoXG4gICAgICAgIChjb29yZHMueCAtIHN0YXJ0UG9pbnQueCkgKiogMiArIChjb29yZHMueSAtIHN0YXJ0UG9pbnQueSkgKiogMlxuICAgICAgKVxuICAgICAgc2V0Q3VycmVudEFubm90YXRpb24oe1xuICAgICAgICAuLi5jdXJyZW50QW5ub3RhdGlvbixcbiAgICAgICAgY29vcmRpbmF0ZXM6IFtzdGFydFBvaW50LngsIHN0YXJ0UG9pbnQueSwgcmFkaXVzXVxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVNb3VzZVVwID0gKCkgPT4ge1xuICAgIGlmIChpc0RyYXdpbmcgJiYgY3VycmVudEFubm90YXRpb24pIHtcbiAgICAgIC8vIE9ubHkgY3JlYXRlIGFubm90YXRpb24gaWYgaXQgaGFzIG1lYW5pbmdmdWwgc2l6ZVxuICAgICAgY29uc3QgaXNWYWxpZEFubm90YXRpb24gPSBcbiAgICAgICAgKGN1cnJlbnRBbm5vdGF0aW9uLnR5cGUgPT09ICdyZWN0YW5nbGUnICYmIFxuICAgICAgICAgTWF0aC5hYnMoY3VycmVudEFubm90YXRpb24uY29vcmRpbmF0ZXNbMl0pID4gNSAmJiBcbiAgICAgICAgIE1hdGguYWJzKGN1cnJlbnRBbm5vdGF0aW9uLmNvb3JkaW5hdGVzWzNdKSA+IDUpIHx8XG4gICAgICAgIChjdXJyZW50QW5ub3RhdGlvbi50eXBlID09PSAnY2lyY2xlJyAmJiBcbiAgICAgICAgIGN1cnJlbnRBbm5vdGF0aW9uLmNvb3JkaW5hdGVzWzJdID4gNSlcblxuICAgICAgaWYgKGlzVmFsaWRBbm5vdGF0aW9uKSB7XG4gICAgICAgIG9uQW5ub3RhdGlvbkNyZWF0ZShjdXJyZW50QW5ub3RhdGlvbilcbiAgICAgIH1cbiAgICB9XG5cbiAgICBzZXRJc0RyYXdpbmcoZmFsc2UpXG4gICAgc2V0U3RhcnRQb2ludChudWxsKVxuICAgIHNldEN1cnJlbnRBbm5vdGF0aW9uKG51bGwpXG4gIH1cblxuICBjb25zdCBoYW5kbGVNb3VzZUxlYXZlID0gKCkgPT4ge1xuICAgIHNldE1vdXNlUG9zaXRpb24obnVsbClcbiAgICBoYW5kbGVNb3VzZVVwKClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURvdWJsZUNsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTENhbnZhc0VsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgY29vcmRzID0gZ2V0Q2FudmFzQ29vcmRpbmF0ZXMoZSlcblxuICAgIC8vIENoZWNrIGlmIGRvdWJsZS1jbGlja2luZyBvbiBhIGxhYmVsIHRvIGVkaXQgaXRcbiAgICBjb25zdCBjbGlja2VkQW5ub3RhdGlvbiA9IGFubm90YXRpb25zLmZpbmQoYW5ub3RhdGlvbiA9PiB7XG4gICAgICBjb25zdCBsYWJlbFggPSBhbm5vdGF0aW9uLmNvb3JkaW5hdGVzWzBdXG4gICAgICBjb25zdCBsYWJlbFkgPSBhbm5vdGF0aW9uLmNvb3JkaW5hdGVzWzFdIC0gOFxuICAgICAgY29uc3QgY2FudmFzID0gY2FudmFzUmVmLmN1cnJlbnRcbiAgICAgIGlmICghY2FudmFzKSByZXR1cm4gZmFsc2VcblxuICAgICAgY29uc3QgY3R4ID0gY2FudmFzLmdldENvbnRleHQoJzJkJylcbiAgICAgIGlmICghY3R4KSByZXR1cm4gZmFsc2VcblxuICAgICAgY3R4LmZvbnQgPSAnYm9sZCAxNHB4IEFyaWFsJ1xuICAgICAgY29uc3QgdGV4dFdpZHRoID0gY3R4Lm1lYXN1cmVUZXh0KGFubm90YXRpb24ubGFiZWwpLndpZHRoXG5cbiAgICAgIHJldHVybiBjb29yZHMueCA+PSBsYWJlbFggLSAyICYmIGNvb3Jkcy54IDw9IGxhYmVsWCArIHRleHRXaWR0aCArIDIgJiZcbiAgICAgICAgICAgICBjb29yZHMueSA+PSBsYWJlbFkgLSAxNiAmJiBjb29yZHMueSA8PSBsYWJlbFkgKyA0XG4gICAgfSlcblxuICAgIGlmIChjbGlja2VkQW5ub3RhdGlvbikge1xuICAgICAgc2V0RWRpdGluZ0xhYmVsKGNsaWNrZWRBbm5vdGF0aW9uLmlkKVxuICAgICAgc2V0RWRpdGluZ0xhYmVsVmFsdWUoY2xpY2tlZEFubm90YXRpb24ubGFiZWwpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQ29udGV4dE1lbnUgPSAoZTogUmVhY3QuTW91c2VFdmVudDxIVE1MQ2FudmFzRWxlbWVudD4pID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcblxuICAgIGNvbnN0IGNvb3JkcyA9IGdldENhbnZhc0Nvb3JkaW5hdGVzKGUpXG4gICAgY29uc3QgcmVjdCA9IGNhbnZhc1JlZi5jdXJyZW50Py5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKVxuICAgIGlmICghcmVjdCkgcmV0dXJuXG5cbiAgICAvLyBDaGVjayBpZiByaWdodC1jbGlja2luZyBvbiBhbiBleGlzdGluZyBhbm5vdGF0aW9uXG4gICAgY29uc3QgY2xpY2tlZEFubm90YXRpb24gPSBhbm5vdGF0aW9ucy5maW5kKGFubm90YXRpb24gPT4ge1xuICAgICAgc3dpdGNoIChhbm5vdGF0aW9uLnR5cGUpIHtcbiAgICAgICAgY2FzZSAncmVjdGFuZ2xlJzpcbiAgICAgICAgICBjb25zdCBbeCwgeSwgd2lkdGgsIGhlaWdodF0gPSBhbm5vdGF0aW9uLmNvb3JkaW5hdGVzXG4gICAgICAgICAgcmV0dXJuIGNvb3Jkcy54ID49IHggJiYgY29vcmRzLnggPD0geCArIHdpZHRoICYmXG4gICAgICAgICAgICAgICAgIGNvb3Jkcy55ID49IHkgJiYgY29vcmRzLnkgPD0geSArIGhlaWdodFxuICAgICAgICBjYXNlICdjaXJjbGUnOlxuICAgICAgICAgIGNvbnN0IFtjeCwgY3ksIHJhZGl1c10gPSBhbm5vdGF0aW9uLmNvb3JkaW5hdGVzXG4gICAgICAgICAgY29uc3QgZGlzdGFuY2UgPSBNYXRoLnNxcnQoKGNvb3Jkcy54IC0gY3gpICoqIDIgKyAoY29vcmRzLnkgLSBjeSkgKiogMilcbiAgICAgICAgICByZXR1cm4gZGlzdGFuY2UgPD0gcmFkaXVzXG4gICAgICAgIGNhc2UgJ3BvaW50JzpcbiAgICAgICAgICBjb25zdCBbcHgsIHB5XSA9IGFubm90YXRpb24uY29vcmRpbmF0ZXNcbiAgICAgICAgICBjb25zdCBwb2ludERpc3RhbmNlID0gTWF0aC5zcXJ0KChjb29yZHMueCAtIHB4KSAqKiAyICsgKGNvb3Jkcy55IC0gcHkpICoqIDIpXG4gICAgICAgICAgcmV0dXJuIHBvaW50RGlzdGFuY2UgPD0gMTBcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgc2V0Q29udGV4dE1lbnUoe1xuICAgICAgc2hvdzogdHJ1ZSxcbiAgICAgIHg6IGUuY2xpZW50WCxcbiAgICAgIHk6IGUuY2xpZW50WSxcbiAgICAgIHRhcmdldEFubm90YXRpb246IGNsaWNrZWRBbm5vdGF0aW9uIHx8IG51bGwsXG4gICAgICBjbGlja1Bvc2l0aW9uOiBjb29yZHNcbiAgICB9KVxuICB9XG5cbiAgY29uc3QgaGlkZUNvbnRleHRNZW51ID0gKCkgPT4ge1xuICAgIHNldENvbnRleHRNZW51KHByZXYgPT4gKHsgLi4ucHJldiwgc2hvdzogZmFsc2UgfSkpXG4gIH1cblxuICAvLyBDYWxjdWxhdGUgbWVudSBwb3NpdGlvbiB3aXRoIGJvdW5kYXJ5IGRldGVjdGlvblxuICBjb25zdCBnZXRNZW51UG9zaXRpb24gPSAoKSA9PiB7XG4gICAgY29uc3QgbWVudVdpZHRoID0gMTkyIC8vIG1pbi13LTQ4ID0gMTkycHhcbiAgICBjb25zdCBtZW51SGVpZ2h0ID0gMjAwIC8vIGVzdGltYXRlZCBtZW51IGhlaWdodFxuICAgIGNvbnN0IG9mZnNldCA9IDEwXG5cbiAgICBsZXQgeCA9IGdsb2JhbE1vdXNlUG9zaXRpb24ueCArIG9mZnNldFxuICAgIGxldCB5ID0gZ2xvYmFsTW91c2VQb3NpdGlvbi55ICsgb2Zmc2V0XG5cbiAgICAvLyBDaGVjayByaWdodCBib3VuZGFyeVxuICAgIGlmICh4ICsgbWVudVdpZHRoID4gd2luZG93LmlubmVyV2lkdGgpIHtcbiAgICAgIHggPSBnbG9iYWxNb3VzZVBvc2l0aW9uLnggLSBtZW51V2lkdGggLSBvZmZzZXRcbiAgICB9XG5cbiAgICAvLyBDaGVjayBib3R0b20gYm91bmRhcnlcbiAgICBpZiAoeSArIG1lbnVIZWlnaHQgPiB3aW5kb3cuaW5uZXJIZWlnaHQpIHtcbiAgICAgIHkgPSBnbG9iYWxNb3VzZVBvc2l0aW9uLnkgLSBtZW51SGVpZ2h0IC0gb2Zmc2V0XG4gICAgfVxuXG4gICAgLy8gRW5zdXJlIG1lbnUgZG9lc24ndCBnbyBvZmYtc2NyZWVuXG4gICAgeCA9IE1hdGgubWF4KDAsIE1hdGgubWluKHgsIHdpbmRvdy5pbm5lcldpZHRoIC0gbWVudVdpZHRoKSlcbiAgICB5ID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oeSwgd2luZG93LmlubmVySGVpZ2h0IC0gbWVudUhlaWdodCkpXG5cbiAgICByZXR1cm4geyB4LCB5IH1cbiAgfVxuXG4gIC8vIENvbnRleHQgbWVudSBhY3Rpb25zXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUFubm90YXRpb24gPSAoKSA9PiB7XG4gICAgaWYgKGNvbnRleHRNZW51LnRhcmdldEFubm90YXRpb24pIHtcbiAgICAgIG9uQW5ub3RhdGlvbkRlbGV0ZShjb250ZXh0TWVudS50YXJnZXRBbm5vdGF0aW9uLmlkKVxuICAgIH1cbiAgICBoaWRlQ29udGV4dE1lbnUoKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRWRpdExhYmVsID0gKCkgPT4ge1xuICAgIGlmIChjb250ZXh0TWVudS50YXJnZXRBbm5vdGF0aW9uKSB7XG4gICAgICBzZXRFZGl0aW5nTGFiZWwoY29udGV4dE1lbnUudGFyZ2V0QW5ub3RhdGlvbi5pZClcbiAgICAgIHNldEVkaXRpbmdMYWJlbFZhbHVlKGNvbnRleHRNZW51LnRhcmdldEFubm90YXRpb24ubGFiZWwpXG4gICAgfVxuICAgIGhpZGVDb250ZXh0TWVudSgpXG4gIH1cblxuICBjb25zdCBoYW5kbGVBZGRBbm5vdGF0aW9uID0gKHR5cGU6ICdyZWN0YW5nbGUnIHwgJ2NpcmNsZScgfCAncG9pbnQnKSA9PiB7XG4gICAgaWYgKCFjb250ZXh0TWVudS5jbGlja1Bvc2l0aW9uKSByZXR1cm5cblxuICAgIGNvbnN0IGNvb3JkcyA9IGNvbnRleHRNZW51LmNsaWNrUG9zaXRpb25cbiAgICBsZXQgbmV3QW5ub3RhdGlvbjogQW5ub3RhdGlvblNoYXBlXG5cbiAgICBpZiAodHlwZSA9PT0gJ3BvaW50Jykge1xuICAgICAgbmV3QW5ub3RhdGlvbiA9IHtcbiAgICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgICAgdHlwZTogJ3BvaW50JyxcbiAgICAgICAgY29vcmRpbmF0ZXM6IFtjb29yZHMueCwgY29vcmRzLnldLFxuICAgICAgICBsYWJlbDogY3VycmVudExhYmVsLFxuICAgICAgICBjb2xvcjogY3VycmVudENvbG9yXG4gICAgICB9XG4gICAgfSBlbHNlIGlmICh0eXBlID09PSAncmVjdGFuZ2xlJykge1xuICAgICAgLy8gQ3JlYXRlIGEgc21hbGwgZGVmYXVsdCByZWN0YW5nbGVcbiAgICAgIG5ld0Fubm90YXRpb24gPSB7XG4gICAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICAgIHR5cGU6ICdyZWN0YW5nbGUnLFxuICAgICAgICBjb29yZGluYXRlczogW2Nvb3Jkcy54IC0gMjUsIGNvb3Jkcy55IC0gMjUsIDUwLCA1MF0sXG4gICAgICAgIGxhYmVsOiBjdXJyZW50TGFiZWwsXG4gICAgICAgIGNvbG9yOiBjdXJyZW50Q29sb3JcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICdjaXJjbGUnKSB7XG4gICAgICAvLyBDcmVhdGUgYSBzbWFsbCBkZWZhdWx0IGNpcmNsZVxuICAgICAgbmV3QW5ub3RhdGlvbiA9IHtcbiAgICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgICAgdHlwZTogJ2NpcmNsZScsXG4gICAgICAgIGNvb3JkaW5hdGVzOiBbY29vcmRzLngsIGNvb3Jkcy55LCAyNV0sXG4gICAgICAgIGxhYmVsOiBjdXJyZW50TGFiZWwsXG4gICAgICAgIGNvbG9yOiBjdXJyZW50Q29sb3JcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgb25Bbm5vdGF0aW9uQ3JlYXRlKG5ld0Fubm90YXRpb24pXG4gICAgaGlkZUNvbnRleHRNZW51KClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUNoYW5nZUxhYmVsID0gKGxhYmVsTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGNvbnRleHRNZW51LnRhcmdldEFubm90YXRpb24pIHtcbiAgICAgIGNvbnN0IGxhYmVsQ29uZmlnID0gcHJlZGVmaW5lZExhYmVscy5maW5kKGwgPT4gbC5uYW1lID09PSBsYWJlbE5hbWUpXG4gICAgICBvbkFubm90YXRpb25VcGRhdGUoY29udGV4dE1lbnUudGFyZ2V0QW5ub3RhdGlvbi5pZCwge1xuICAgICAgICBsYWJlbDogbGFiZWxOYW1lLFxuICAgICAgICBjb2xvcjogbGFiZWxDb25maWc/LmNvbG9yIHx8IGN1cnJlbnRDb2xvclxuICAgICAgfSlcbiAgICB9XG4gICAgaGlkZUNvbnRleHRNZW51KClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBiZy13aGl0ZVwiXG4gICAgICBzdHlsZT17eyB3aWR0aDogaW1hZ2VXaWR0aCwgaGVpZ2h0OiBpbWFnZUhlaWdodCB9fVxuICAgID5cbiAgICAgIHshaW1hZ2VMb2FkZWQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS0xMDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvIG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPuWKoOi9veWbvuWDj+S4rS4uLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICA8aW1nXG4gICAgICAgIHJlZj17aW1hZ2VSZWZ9XG4gICAgICAgIHNyYz17aW1hZ2VVcmx9XG4gICAgICAgIGFsdD1cIkFubm90YXRpb24gdGFyZ2V0XCJcbiAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHBvaW50ZXItZXZlbnRzLW5vbmVcIlxuICAgICAgICBzdHlsZT17eyB3aWR0aDogaW1hZ2VXaWR0aCwgaGVpZ2h0OiBpbWFnZUhlaWdodCB9fVxuICAgICAgICBvbkxvYWQ9eygpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnSW1hZ2UgbG9hZGVkIHN1Y2Nlc3NmdWxseTonLCBpbWFnZVVybClcbiAgICAgICAgICBzZXRJbWFnZUxvYWRlZCh0cnVlKVxuICAgICAgICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50XG4gICAgICAgICAgaWYgKGNhbnZhcykge1xuICAgICAgICAgICAgY29uc3QgY3R4ID0gY2FudmFzLmdldENvbnRleHQoJzJkJylcbiAgICAgICAgICAgIGlmIChjdHgpIGRyYXdBbm5vdGF0aW9ucyhjdHgpXG4gICAgICAgICAgfVxuICAgICAgICB9fVxuICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ltYWdlIGZhaWxlZCB0byBsb2FkOicsIGltYWdlVXJsLCBlKVxuICAgICAgICAgIHNldEltYWdlTG9hZGVkKGZhbHNlKVxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICAgIDxjYW52YXNcbiAgICAgICAgcmVmPXtjYW52YXNSZWZ9XG4gICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMCBjdXJzb3ItY3Jvc3NoYWlyXCJcbiAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGltYWdlV2lkdGgsIGhlaWdodDogaW1hZ2VIZWlnaHQgfX1cbiAgICAgICAgb25Nb3VzZURvd249e2hhbmRsZU1vdXNlRG93bn1cbiAgICAgICAgb25Nb3VzZU1vdmU9e2hhbmRsZU1vdXNlTW92ZX1cbiAgICAgICAgb25Nb3VzZVVwPXtoYW5kbGVNb3VzZVVwfVxuICAgICAgICBvbk1vdXNlTGVhdmU9e2hhbmRsZU1vdXNlTGVhdmV9XG4gICAgICAgIG9uRG91YmxlQ2xpY2s9e2hhbmRsZURvdWJsZUNsaWNrfVxuICAgICAgICBvbkNvbnRleHRNZW51PXtoYW5kbGVDb250ZXh0TWVudX1cbiAgICAgICAgb25DbGljaz17aGlkZUNvbnRleHRNZW51fVxuICAgICAgLz5cblxuICAgICAgey8qIExhYmVsIGVkaXRpbmcgaW5wdXQgKi99XG4gICAgICB7ZWRpdGluZ0xhYmVsICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdMYWJlbFZhbHVlfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0aW5nTGFiZWxWYWx1ZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBvbkJsdXI9eygpID0+IHtcbiAgICAgICAgICAgICAgaWYgKGVkaXRpbmdMYWJlbFZhbHVlLnRyaW0oKSkge1xuICAgICAgICAgICAgICAgIG9uQW5ub3RhdGlvblVwZGF0ZShlZGl0aW5nTGFiZWwsIHsgbGFiZWw6IGVkaXRpbmdMYWJlbFZhbHVlLnRyaW0oKSB9KVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIHNldEVkaXRpbmdMYWJlbChudWxsKVxuICAgICAgICAgICAgICBzZXRFZGl0aW5nTGFiZWxWYWx1ZSgnJylcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBvbktleURvd249eyhlKSA9PiB7XG4gICAgICAgICAgICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJykge1xuICAgICAgICAgICAgICAgIGlmIChlZGl0aW5nTGFiZWxWYWx1ZS50cmltKCkpIHtcbiAgICAgICAgICAgICAgICAgIG9uQW5ub3RhdGlvblVwZGF0ZShlZGl0aW5nTGFiZWwsIHsgbGFiZWw6IGVkaXRpbmdMYWJlbFZhbHVlLnRyaW0oKSB9KVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBzZXRFZGl0aW5nTGFiZWwobnVsbClcbiAgICAgICAgICAgICAgICBzZXRFZGl0aW5nTGFiZWxWYWx1ZSgnJylcbiAgICAgICAgICAgICAgfSBlbHNlIGlmIChlLmtleSA9PT0gJ0VzY2FwZScpIHtcbiAgICAgICAgICAgICAgICBzZXRFZGl0aW5nTGFiZWwobnVsbClcbiAgICAgICAgICAgICAgICBzZXRFZGl0aW5nTGFiZWxWYWx1ZSgnJylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInBvaW50ZXItZXZlbnRzLWF1dG8gcHgtMiBweS0xIHRleHQtc20gYm9yZGVyIGJvcmRlci1ibHVlLTUwMCByb3VuZGVkIGJnLXdoaXRlIHNoYWRvdy1sZ1wiXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgbGVmdDogYW5ub3RhdGlvbnMuZmluZChhID0+IGEuaWQgPT09IGVkaXRpbmdMYWJlbCk/LmNvb3JkaW5hdGVzWzBdIHx8IDAsXG4gICAgICAgICAgICAgIHRvcDogKGFubm90YXRpb25zLmZpbmQoYSA9PiBhLmlkID09PSBlZGl0aW5nTGFiZWwpPy5jb29yZGluYXRlc1sxXSB8fCAwKSAtIDMwLFxuICAgICAgICAgICAgICB6SW5kZXg6IDEwMDBcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBhdXRvRm9jdXNcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBDb250ZXh0IE1lbnUgKi99XG4gICAgICB7Y29udGV4dE1lbnUuc2hvdyAmJiAoXG4gICAgICAgIDw+XG4gICAgICAgICAgey8qIEJhY2tkcm9wIHRvIGNsb3NlIG1lbnUgKi99XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTQwXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hpZGVDb250ZXh0TWVudX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgey8qIE1lbnUgKi99XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgei01MCBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXIgcHktMiBtaW4tdy00OCBwb2ludGVyLWV2ZW50cy1hdXRvXCJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGxlZnQ6IGdsb2JhbE1vdXNlUG9zaXRpb24ueCArIDEwLFxuICAgICAgICAgICAgICB0b3A6IGdsb2JhbE1vdXNlUG9zaXRpb24ueSArIDEwLFxuICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGUoMCwgMCknLCAvLyBFbnN1cmUgc21vb3RoIHBvc2l0aW9uaW5nXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtjb250ZXh0TWVudS50YXJnZXRBbm5vdGF0aW9uID8gKFxuICAgICAgICAgICAgICAvLyBNZW51IGZvciBleGlzdGluZyBhbm5vdGF0aW9uXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJvcmRlci1iXCI+XG4gICAgICAgICAgICAgICAgICB7Y29udGV4dE1lbnUudGFyZ2V0QW5ub3RhdGlvbi5sYWJlbH0gKHtjb250ZXh0TWVudS50YXJnZXRBbm5vdGF0aW9uLnR5cGV9KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiB0ZXh0LWxlZnQgdGV4dC1zbSBob3ZlcjpiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFZGl0TGFiZWx9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPuKcj++4jzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIOe8lui+keagh+etvlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0zIHB5LTEgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+5pu05pS55qCH562+5Li6OjwvZGl2PlxuICAgICAgICAgICAgICAgIHtwcmVkZWZpbmVkTGFiZWxzLm1hcCgobGFiZWwpID0+IChcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAga2V5PXtsYWJlbC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0xIHRleHQtbGVmdCB0ZXh0LXNtIGhvdmVyOmJnLWdyYXktMTAwIGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQ2hhbmdlTGFiZWwobGFiZWwubmFtZSl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQgbXItMiBib3JkZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogbGFiZWwuY29sb3IgfX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAge2xhYmVsLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgbXktMVwiIC8+XG5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIHRleHQtbGVmdCB0ZXh0LXNtIGhvdmVyOmJnLXJlZC01MCB0ZXh0LXJlZC02MDAgZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRGVsZXRlQW5ub3RhdGlvbn1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0yXCI+8J+Xke+4jzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIOWIoOmZpOagh+azqFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIC8vIE1lbnUgZm9yIGVtcHR5IGFyZWFcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYm9yZGVyLWJcIj5cbiAgICAgICAgICAgICAgICAgIOa3u+WKoOagh+azqFxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiB0ZXh0LWxlZnQgdGV4dC1zbSBob3ZlcjpiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBZGRBbm5vdGF0aW9uKCdwb2ludCcpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj7wn5ONPC9zcGFuPlxuICAgICAgICAgICAgICAgICAg5re75Yqg54K55qCH5rOoXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIHRleHQtbGVmdCB0ZXh0LXNtIGhvdmVyOmJnLWdyYXktMTAwIGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFkZEFubm90YXRpb24oJ3JlY3RhbmdsZScpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj7irJw8L3NwYW4+XG4gICAgICAgICAgICAgICAgICDmt7vliqDnn6nlvaLmoIfms6hcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgdGV4dC1sZWZ0IHRleHQtc20gaG92ZXI6YmctZ3JheS0xMDAgZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQWRkQW5ub3RhdGlvbignY2lyY2xlJyl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPuKtlTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIOa3u+WKoOWchuW9ouagh+azqFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVJlZiIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJJbWFnZUFubm90YXRpb25DYW52YXMiLCJpbWFnZVVybCIsImltYWdlV2lkdGgiLCJpbWFnZUhlaWdodCIsImFubm90YXRpb25zIiwic2VsZWN0ZWRBbm5vdGF0aW9uIiwiY3VycmVudFRvb2wiLCJjdXJyZW50TGFiZWwiLCJjdXJyZW50Q29sb3IiLCJ6b29tIiwic2hvd0xhYmVscyIsInByZWRlZmluZWRMYWJlbHMiLCJvbkFubm90YXRpb25DcmVhdGUiLCJvbkFubm90YXRpb25VcGRhdGUiLCJvbkFubm90YXRpb25TZWxlY3QiLCJvbkFubm90YXRpb25EZWxldGUiLCJjYW52YXNSZWYiLCJpbWFnZVJlZiIsImlzRHJhd2luZyIsInNldElzRHJhd2luZyIsInN0YXJ0UG9pbnQiLCJzZXRTdGFydFBvaW50IiwiY3VycmVudEFubm90YXRpb24iLCJzZXRDdXJyZW50QW5ub3RhdGlvbiIsImltYWdlTG9hZGVkIiwic2V0SW1hZ2VMb2FkZWQiLCJtb3VzZVBvc2l0aW9uIiwic2V0TW91c2VQb3NpdGlvbiIsImVkaXRpbmdMYWJlbCIsInNldEVkaXRpbmdMYWJlbCIsImVkaXRpbmdMYWJlbFZhbHVlIiwic2V0RWRpdGluZ0xhYmVsVmFsdWUiLCJjb250ZXh0TWVudSIsInNldENvbnRleHRNZW51Iiwic2hvdyIsIngiLCJ5IiwidGFyZ2V0QW5ub3RhdGlvbiIsImNsaWNrUG9zaXRpb24iLCJnbG9iYWxNb3VzZVBvc2l0aW9uIiwic2V0R2xvYmFsTW91c2VQb3NpdGlvbiIsImNvbnNvbGUiLCJsb2ciLCJkcmF3QW5ub3RhdGlvbnMiLCJjdHgiLCJjbGVhclJlY3QiLCJjYW52YXMiLCJ3aWR0aCIsImhlaWdodCIsImltYWdlIiwiY3VycmVudCIsImNvbXBsZXRlIiwiZHJhd0ltYWdlIiwiZm9yRWFjaCIsImFubm90YXRpb24iLCJzdHJva2VTdHlsZSIsImNvbG9yIiwiZmlsbFN0eWxlIiwibGluZVdpZHRoIiwiaWQiLCJ0eXBlIiwiY29vcmRpbmF0ZXMiLCJzdHJva2VSZWN0IiwiZmlsbFJlY3QiLCJjeCIsImN5IiwicmFkaXVzIiwiYmVnaW5QYXRoIiwiYXJjIiwiTWF0aCIsIlBJIiwic3Ryb2tlIiwiZmlsbCIsInB4IiwicHkiLCJsYWJlbCIsImZvbnQiLCJsYWJlbFgiLCJsYWJlbFkiLCJzdHJva2VUZXh0IiwiZmlsbFRleHQiLCJ0ZXh0V2lkdGgiLCJtZWFzdXJlVGV4dCIsInNldExpbmVEYXNoIiwiY29vcmRUZXh0Iiwicm91bmQiLCJkaXNwbGF5WCIsImRpc3BsYXlZIiwiZ2V0Q29udGV4dCIsImhhbmRsZUdsb2JhbE1vdXNlTW92ZSIsImUiLCJjbGllbnRYIiwiY2xpZW50WSIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJnZXRDYW52YXNDb29yZGluYXRlcyIsInJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJsZWZ0IiwidG9wIiwiaGFuZGxlTW91c2VEb3duIiwiY29vcmRzIiwiY2xpY2tlZEFubm90YXRpb24iLCJmaW5kIiwiZGlzdGFuY2UiLCJzcXJ0IiwicG9pbnREaXN0YW5jZSIsIm5ld0Fubm90YXRpb24iLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJoYW5kbGVNb3VzZU1vdmUiLCJoYW5kbGVNb3VzZVVwIiwiaXNWYWxpZEFubm90YXRpb24iLCJhYnMiLCJoYW5kbGVNb3VzZUxlYXZlIiwiaGFuZGxlRG91YmxlQ2xpY2siLCJoYW5kbGVDb250ZXh0TWVudSIsInByZXZlbnREZWZhdWx0IiwiaGlkZUNvbnRleHRNZW51IiwicHJldiIsImdldE1lbnVQb3NpdGlvbiIsIm1lbnVXaWR0aCIsIm1lbnVIZWlnaHQiLCJvZmZzZXQiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiaW5uZXJIZWlnaHQiLCJtYXgiLCJtaW4iLCJoYW5kbGVEZWxldGVBbm5vdGF0aW9uIiwiaGFuZGxlRWRpdExhYmVsIiwiaGFuZGxlQWRkQW5ub3RhdGlvbiIsImhhbmRsZUNoYW5nZUxhYmVsIiwibGFiZWxOYW1lIiwibGFiZWxDb25maWciLCJsIiwibmFtZSIsImRpdiIsImNsYXNzTmFtZSIsInN0eWxlIiwicCIsImltZyIsInJlZiIsInNyYyIsImFsdCIsIm9uTG9hZCIsIm9uRXJyb3IiLCJlcnJvciIsIm9uTW91c2VEb3duIiwib25Nb3VzZU1vdmUiLCJvbk1vdXNlVXAiLCJvbk1vdXNlTGVhdmUiLCJvbkRvdWJsZUNsaWNrIiwib25Db250ZXh0TWVudSIsIm9uQ2xpY2siLCJpbnB1dCIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJvbkJsdXIiLCJ0cmltIiwib25LZXlEb3duIiwia2V5IiwicG9zaXRpb24iLCJhIiwiekluZGV4IiwiYXV0b0ZvY3VzIiwidHJhbnNmb3JtIiwiYnV0dG9uIiwic3BhbiIsIm1hcCIsImJhY2tncm91bmRDb2xvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
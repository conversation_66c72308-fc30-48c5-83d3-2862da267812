"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    const [globalMousePosition, setGlobalMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    // Global mouse tracking for context menu following\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const handleGlobalMouseMove = {\n                \"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\": (e)=>{\n                    setGlobalMousePosition({\n                        x: e.clientX,\n                        y: e.clientY\n                    });\n                }\n            }[\"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\"];\n            if (contextMenu.show) {\n                document.addEventListener('mousemove', handleGlobalMouseMove);\n                return ({\n                    \"ImageAnnotationCanvas.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleGlobalMouseMove);\n                    }\n                })[\"ImageAnnotationCanvas.useEffect\"];\n            }\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        contextMenu.show\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    const handleContextMenu = (e)=>{\n        var _canvasRef_current;\n        e.preventDefault();\n        const coords = getCanvasCoordinates(e);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // Check if right-clicking on an existing annotation\n        const clickedAnnotation = annotations.find((annotation)=>{\n            switch(annotation.type){\n                case 'rectangle':\n                    const [x, y, width, height] = annotation.coordinates;\n                    return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                case 'circle':\n                    const [cx, cy, radius] = annotation.coordinates;\n                    const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                    return distance <= radius;\n                case 'point':\n                    const [px, py] = annotation.coordinates;\n                    const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                    return pointDistance <= 10;\n                default:\n                    return false;\n            }\n        });\n        setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            targetAnnotation: clickedAnnotation || null,\n            clickPosition: coords\n        });\n    };\n    const hideContextMenu = ()=>{\n        setContextMenu((prev)=>({\n                ...prev,\n                show: false\n            }));\n    };\n    // Context menu actions\n    const handleDeleteAnnotation = ()=>{\n        if (contextMenu.targetAnnotation) {\n            onAnnotationDelete(contextMenu.targetAnnotation.id);\n        }\n        hideContextMenu();\n    };\n    const handleEditLabel = ()=>{\n        if (contextMenu.targetAnnotation) {\n            setEditingLabel(contextMenu.targetAnnotation.id);\n            setEditingLabelValue(contextMenu.targetAnnotation.label);\n        }\n        hideContextMenu();\n    };\n    const handleAddAnnotation = (type)=>{\n        if (!contextMenu.clickPosition) return;\n        const coords = contextMenu.clickPosition;\n        let newAnnotation;\n        if (type === 'point') {\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'rectangle') {\n            // Create a small default rectangle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x - 25,\n                    coords.y - 25,\n                    50,\n                    50\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'circle') {\n            // Create a small default circle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    25\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else {\n            return;\n        }\n        onAnnotationCreate(newAnnotation);\n        hideContextMenu();\n    };\n    const handleChangeLabel = (labelName)=>{\n        if (contextMenu.targetAnnotation) {\n            const labelConfig = predefinedLabels.find((l)=>l.name === labelName);\n            onAnnotationUpdate(contextMenu.targetAnnotation.id, {\n                label: labelName,\n                color: (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || currentColor\n            });\n        }\n        hideContextMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 493,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick,\n                onContextMenu: handleContextMenu,\n                onClick: hideContextMenu\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 536,\n                columnNumber: 9\n            }, this),\n            contextMenu.show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: hideContextMenu\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed z-50 bg-white rounded-lg shadow-lg border py-2 min-w-48\",\n                        style: {\n                            left: contextMenu.x,\n                            top: contextMenu.y\n                        },\n                        children: contextMenu.targetAnnotation ? // Menu for existing annotation\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: [\n                                        contextMenu.targetAnnotation.label,\n                                        \" (\",\n                                        contextMenu.targetAnnotation.type,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: handleEditLabel,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"编辑标签\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1 text-xs text-gray-500\",\n                                    children: \"更改标签为:\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 17\n                                }, this),\n                                predefinedLabels.map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full px-3 py-1 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                        onClick: ()=>handleChangeLabel(label.name),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded mr-2 border\",\n                                                style: {\n                                                    backgroundColor: label.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 21\n                                            }, this),\n                                            label.name\n                                        ]\n                                    }, label.name, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t my-1\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center\",\n                                    onClick: handleDeleteAnnotation,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDDD1️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"删除标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : // Menu for empty area\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: \"添加标注\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('point'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDCCD\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加点标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('rectangle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⬜\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加矩形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('circle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⭕\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加圆形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"IDHObakyPsHr+JpRBpdLTwKuRL0=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
'use client'

import { useRef, useEffect, useState, useCallback } from 'react'

export interface AnnotationShape {
  id: string
  type: 'rectangle' | 'circle' | 'point'
  coordinates: number[]
  label: string
  color: string
  confidence?: number
}

interface ImageAnnotationCanvasProps {
  imageUrl: string
  imageWidth: number
  imageHeight: number
  annotations: AnnotationShape[]
  selectedAnnotation?: string | null
  currentTool: 'select' | 'rectangle' | 'circle' | 'point'
  currentLabel: string
  currentColor: string
  zoom: number
  showLabels: boolean
  predefinedLabels: Array<{ name: string; color: string }>
  onAnnotationCreate: (annotation: AnnotationShape) => void
  onAnnotationUpdate: (id: string, annotation: Partial<AnnotationShape>) => void
  onAnnotationSelect: (id: string | null) => void
  onAnnotationDelete: (id: string) => void
}

export function ImageAnnotationCanvas({
  imageUrl,
  imageWidth,
  imageHeight,
  annotations,
  selectedAnnotation,
  currentTool,
  currentLabel,
  currentColor,
  zoom,
  showLabels,
  predefinedLabels,
  onAnnotationCreate,
  onAnnotationUpdate,
  onAnnotationSelect,
  onAnnotationDelete,
}: ImageAnnotationCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null)
  const [currentAnnotation, setCurrentAnnotation] = useState<AnnotationShape | null>(null)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null)
  const [editingLabel, setEditingLabel] = useState<string | null>(null)
  const [editingLabelValue, setEditingLabelValue] = useState('')
  const [contextMenu, setContextMenu] = useState<{
    show: boolean
    x: number
    y: number
    targetAnnotation: AnnotationShape | null
    clickPosition: { x: number; y: number } | null
  }>({
    show: false,
    x: 0,
    y: 0,
    targetAnnotation: null,
    clickPosition: null
  })

  // Debug logging
  useEffect(() => {
    console.log('ImageAnnotationCanvas props:', {
      imageUrl,
      imageWidth,
      imageHeight,
      imageLoaded
    })
  }, [imageUrl, imageWidth, imageHeight, imageLoaded])

  const drawAnnotations = useCallback((ctx: CanvasRenderingContext2D) => {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height)
    
    // Draw image
    const image = imageRef.current
    if (image && image.complete) {
      ctx.drawImage(image, 0, 0, imageWidth, imageHeight)
    }

    // Draw annotations
    annotations.forEach((annotation) => {
      ctx.strokeStyle = annotation.color
      ctx.fillStyle = annotation.color + '30' // 30% opacity
      ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2

      switch (annotation.type) {
        case 'rectangle':
          const [x, y, width, height] = annotation.coordinates
          ctx.strokeRect(x, y, width, height)
          ctx.fillRect(x, y, width, height)
          break

        case 'circle':
          const [cx, cy, radius] = annotation.coordinates
          ctx.beginPath()
          ctx.arc(cx, cy, radius, 0, 2 * Math.PI)
          ctx.stroke()
          ctx.fill()
          break

        case 'point':
          const [px, py] = annotation.coordinates
          ctx.beginPath()
          ctx.arc(px, py, 5, 0, 2 * Math.PI)
          ctx.fill()
          break


      }

      // Draw label
      if (showLabels && annotation.label) {
        ctx.fillStyle = annotation.color
        ctx.font = 'bold 14px Arial'
        ctx.strokeStyle = 'white'
        ctx.lineWidth = 3

        const labelX = annotation.coordinates[0]
        const labelY = annotation.coordinates[1] - 8

        // Draw text outline
        ctx.strokeText(annotation.label, labelX, labelY)
        // Draw text
        ctx.fillText(annotation.label, labelX, labelY)

        // Draw edit indicator for selected annotation
        if (annotation.id === selectedAnnotation) {
          ctx.fillStyle = 'rgba(59, 130, 246, 0.2)'
          ctx.strokeStyle = '#3b82f6'
          ctx.lineWidth = 1
          const textWidth = ctx.measureText(annotation.label).width
          ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20)
          ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20)
        }
      }
    })

    // Draw current annotation being created
    if (currentAnnotation) {
      ctx.strokeStyle = currentColor
      ctx.fillStyle = currentColor + '30'
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])

      switch (currentAnnotation.type) {
        case 'rectangle':
          const [x, y, width, height] = currentAnnotation.coordinates
          ctx.strokeRect(x, y, width, height)
          break

        case 'circle':
          const [cx, cy, radius] = currentAnnotation.coordinates
          ctx.beginPath()
          ctx.arc(cx, cy, radius, 0, 2 * Math.PI)
          ctx.stroke()
          break
      }

      ctx.setLineDash([])
    }

    // Draw mouse coordinates
    if (mousePosition) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
      ctx.font = '12px Arial'
      const coordText = `(${Math.round(mousePosition.x)}, ${Math.round(mousePosition.y)})`
      const textWidth = ctx.measureText(coordText).width

      // Position the coordinate display near the mouse but within canvas bounds
      let displayX = mousePosition.x + 10
      let displayY = mousePosition.y - 10

      // Adjust position if it would go outside canvas
      if (displayX + textWidth + 10 > imageWidth) {
        displayX = mousePosition.x - textWidth - 10
      }
      if (displayY < 20) {
        displayY = mousePosition.y + 25
      }

      // Draw background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
      ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20)

      // Draw text
      ctx.fillStyle = 'white'
      ctx.fillText(coordText, displayX, displayY)
    }
  }, [annotations, selectedAnnotation, showLabels, currentAnnotation, imageWidth, imageHeight, currentColor, mousePosition])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = imageWidth
    canvas.height = imageHeight

    drawAnnotations(ctx)
  }, [drawAnnotations])

  const getCanvasCoordinates = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const rect = canvas.getBoundingClientRect()
    return {
      x: (e.clientX - rect.left) / zoom,
      y: (e.clientY - rect.top) / zoom
    }
  }

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const coords = getCanvasCoordinates(e)

    if (currentTool === 'select') {
      // Check if clicking on an existing annotation
      const clickedAnnotation = annotations.find(annotation => {
        switch (annotation.type) {
          case 'rectangle':
            const [x, y, width, height] = annotation.coordinates
            return coords.x >= x && coords.x <= x + width && 
                   coords.y >= y && coords.y <= y + height
          case 'circle':
            const [cx, cy, radius] = annotation.coordinates
            const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2)
            return distance <= radius
          case 'point':
            const [px, py] = annotation.coordinates
            const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2)
            return pointDistance <= 10
          default:
            return false
        }
      })

      onAnnotationSelect(clickedAnnotation?.id || null)
      return
    }

    if (currentTool === 'point') {
      const newAnnotation: AnnotationShape = {
        id: Date.now().toString(),
        type: 'point',
        coordinates: [coords.x, coords.y],
        label: currentLabel,
        color: currentColor
      }
      onAnnotationCreate(newAnnotation)
      return
    }

    setIsDrawing(true)
    setStartPoint(coords)

    if (currentTool === 'rectangle') {
      setCurrentAnnotation({
        id: Date.now().toString(),
        type: 'rectangle',
        coordinates: [coords.x, coords.y, 0, 0],
        label: currentLabel,
        color: currentColor
      })
    } else if (currentTool === 'circle') {
      setCurrentAnnotation({
        id: Date.now().toString(),
        type: 'circle',
        coordinates: [coords.x, coords.y, 0],
        label: currentLabel,
        color: currentColor
      })
    }
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const coords = getCanvasCoordinates(e)

    // Always update mouse position for coordinate display
    setMousePosition(coords)

    if (!isDrawing || !startPoint || !currentAnnotation) return

    if (currentAnnotation.type === 'rectangle') {
      const width = coords.x - startPoint.x
      const height = coords.y - startPoint.y
      setCurrentAnnotation({
        ...currentAnnotation,
        coordinates: [startPoint.x, startPoint.y, width, height]
      })
    } else if (currentAnnotation.type === 'circle') {
      const radius = Math.sqrt(
        (coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2
      )
      setCurrentAnnotation({
        ...currentAnnotation,
        coordinates: [startPoint.x, startPoint.y, radius]
      })
    }
  }

  const handleMouseUp = () => {
    if (isDrawing && currentAnnotation) {
      // Only create annotation if it has meaningful size
      const isValidAnnotation = 
        (currentAnnotation.type === 'rectangle' && 
         Math.abs(currentAnnotation.coordinates[2]) > 5 && 
         Math.abs(currentAnnotation.coordinates[3]) > 5) ||
        (currentAnnotation.type === 'circle' && 
         currentAnnotation.coordinates[2] > 5)

      if (isValidAnnotation) {
        onAnnotationCreate(currentAnnotation)
      }
    }

    setIsDrawing(false)
    setStartPoint(null)
    setCurrentAnnotation(null)
  }

  const handleMouseLeave = () => {
    setMousePosition(null)
    handleMouseUp()
  }

  const handleDoubleClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const coords = getCanvasCoordinates(e)

    // Check if double-clicking on a label to edit it
    const clickedAnnotation = annotations.find(annotation => {
      const labelX = annotation.coordinates[0]
      const labelY = annotation.coordinates[1] - 8
      const canvas = canvasRef.current
      if (!canvas) return false

      const ctx = canvas.getContext('2d')
      if (!ctx) return false

      ctx.font = 'bold 14px Arial'
      const textWidth = ctx.measureText(annotation.label).width

      return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 &&
             coords.y >= labelY - 16 && coords.y <= labelY + 4
    })

    if (clickedAnnotation) {
      setEditingLabel(clickedAnnotation.id)
      setEditingLabelValue(clickedAnnotation.label)
    }
  }

  const handleContextMenu = (e: React.MouseEvent<HTMLCanvasElement>) => {
    e.preventDefault()

    const coords = getCanvasCoordinates(e)
    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    // Check if right-clicking on an existing annotation
    const clickedAnnotation = annotations.find(annotation => {
      switch (annotation.type) {
        case 'rectangle':
          const [x, y, width, height] = annotation.coordinates
          return coords.x >= x && coords.x <= x + width &&
                 coords.y >= y && coords.y <= y + height
        case 'circle':
          const [cx, cy, radius] = annotation.coordinates
          const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2)
          return distance <= radius
        case 'point':
          const [px, py] = annotation.coordinates
          const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2)
          return pointDistance <= 10
        default:
          return false
      }
    })

    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      targetAnnotation: clickedAnnotation || null,
      clickPosition: coords
    })
  }

  const hideContextMenu = () => {
    setContextMenu(prev => ({ ...prev, show: false }))
  }

  // Context menu actions
  const handleDeleteAnnotation = () => {
    if (contextMenu.targetAnnotation) {
      onAnnotationDelete(contextMenu.targetAnnotation.id)
    }
    hideContextMenu()
  }

  const handleEditLabel = () => {
    if (contextMenu.targetAnnotation) {
      setEditingLabel(contextMenu.targetAnnotation.id)
      setEditingLabelValue(contextMenu.targetAnnotation.label)
    }
    hideContextMenu()
  }

  const handleAddAnnotation = (type: 'rectangle' | 'circle' | 'point') => {
    if (!contextMenu.clickPosition) return

    const coords = contextMenu.clickPosition
    let newAnnotation: AnnotationShape

    if (type === 'point') {
      newAnnotation = {
        id: Date.now().toString(),
        type: 'point',
        coordinates: [coords.x, coords.y],
        label: currentLabel,
        color: currentColor
      }
    } else if (type === 'rectangle') {
      // Create a small default rectangle
      newAnnotation = {
        id: Date.now().toString(),
        type: 'rectangle',
        coordinates: [coords.x - 25, coords.y - 25, 50, 50],
        label: currentLabel,
        color: currentColor
      }
    } else if (type === 'circle') {
      // Create a small default circle
      newAnnotation = {
        id: Date.now().toString(),
        type: 'circle',
        coordinates: [coords.x, coords.y, 25],
        label: currentLabel,
        color: currentColor
      }
    } else {
      return
    }

    onAnnotationCreate(newAnnotation)
    hideContextMenu()
  }

  const handleChangeLabel = (labelName: string) => {
    if (contextMenu.targetAnnotation) {
      const labelConfig = predefinedLabels.find(l => l.name === labelName)
      onAnnotationUpdate(contextMenu.targetAnnotation.id, {
        label: labelName,
        color: labelConfig?.color || currentColor
      })
    }
    hideContextMenu()
  }

  return (
    <div
      className="relative border border-gray-300 rounded-lg overflow-hidden bg-white"
      style={{ width: imageWidth, height: imageHeight }}
    >
      {!imageLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">加载图像中...</p>
          </div>
        </div>
      )}

      <img
        ref={imageRef}
        src={imageUrl}
        alt="Annotation target"
        className="absolute top-0 left-0 pointer-events-none"
        style={{ width: imageWidth, height: imageHeight }}
        onLoad={() => {
          console.log('Image loaded successfully:', imageUrl)
          setImageLoaded(true)
          const canvas = canvasRef.current
          if (canvas) {
            const ctx = canvas.getContext('2d')
            if (ctx) drawAnnotations(ctx)
          }
        }}
        onError={(e) => {
          console.error('Image failed to load:', imageUrl, e)
          setImageLoaded(false)
        }}
      />
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 cursor-crosshair"
        style={{ width: imageWidth, height: imageHeight }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onDoubleClick={handleDoubleClick}
        onContextMenu={handleContextMenu}
        onClick={hideContextMenu}
      />

      {/* Label editing input */}
      {editingLabel && (
        <div className="absolute top-0 left-0 pointer-events-none">
          <input
            type="text"
            value={editingLabelValue}
            onChange={(e) => setEditingLabelValue(e.target.value)}
            onBlur={() => {
              if (editingLabelValue.trim()) {
                onAnnotationUpdate(editingLabel, { label: editingLabelValue.trim() })
              }
              setEditingLabel(null)
              setEditingLabelValue('')
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                if (editingLabelValue.trim()) {
                  onAnnotationUpdate(editingLabel, { label: editingLabelValue.trim() })
                }
                setEditingLabel(null)
                setEditingLabelValue('')
              } else if (e.key === 'Escape') {
                setEditingLabel(null)
                setEditingLabelValue('')
              }
            }}
            className="pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg"
            style={{
              position: 'absolute',
              left: annotations.find(a => a.id === editingLabel)?.coordinates[0] || 0,
              top: (annotations.find(a => a.id === editingLabel)?.coordinates[1] || 0) - 30,
              zIndex: 1000
            }}
            autoFocus
          />
        </div>
      )}

      {/* Context Menu */}
      {contextMenu.show && (
        <>
          {/* Backdrop to close menu */}
          <div
            className="fixed inset-0 z-40"
            onClick={hideContextMenu}
          />

          {/* Menu */}
          <div
            className="fixed z-50 bg-white rounded-lg shadow-lg border py-2 min-w-48"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
          >
            {contextMenu.targetAnnotation ? (
              // Menu for existing annotation
              <>
                <div className="px-3 py-2 text-sm font-medium text-gray-700 border-b">
                  {contextMenu.targetAnnotation.label} ({contextMenu.targetAnnotation.type})
                </div>

                <button
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  onClick={handleEditLabel}
                >
                  <span className="mr-2">✏️</span>
                  编辑标签
                </button>

                <div className="px-3 py-1 text-xs text-gray-500">更改标签为:</div>
                {predefinedLabels.map((label) => (
                  <button
                    key={label.name}
                    className="w-full px-3 py-1 text-left text-sm hover:bg-gray-100 flex items-center"
                    onClick={() => handleChangeLabel(label.name)}
                  >
                    <div
                      className="w-3 h-3 rounded mr-2 border"
                      style={{ backgroundColor: label.color }}
                    />
                    {label.name}
                  </button>
                ))}

                <div className="border-t my-1" />

                <button
                  className="w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center"
                  onClick={handleDeleteAnnotation}
                >
                  <span className="mr-2">🗑️</span>
                  删除标注
                </button>
              </>
            ) : (
              // Menu for empty area
              <>
                <div className="px-3 py-2 text-sm font-medium text-gray-700 border-b">
                  添加标注
                </div>

                <button
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  onClick={() => handleAddAnnotation('point')}
                >
                  <span className="mr-2">📍</span>
                  添加点标注
                </button>

                <button
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  onClick={() => handleAddAnnotation('rectangle')}
                >
                  <span className="mr-2">⬜</span>
                  添加矩形标注
                </button>

                <button
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  onClick={() => handleAddAnnotation('circle')}
                >
                  <span className="mr-2">⭕</span>
                  添加圆形标注
                </button>
              </>
            )}
          </div>
        </>
      )}
    </div>
  )
}

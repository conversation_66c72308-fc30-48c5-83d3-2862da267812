'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  MousePointer, 
  Square, 
  Circle, 
  MapPin, 
  Eye, 
  EyeOff, 
  ZoomIn, 
  ZoomOut, 
  Undo, 
  Redo, 
  Save, 
  Trash2,
  <PERSON><PERSON>,
  <PERSON>
} from 'lucide-react'

export type AnnotationTool = 'select' | 'rectangle' | 'circle' | 'point'

interface CompactAnnotationToolbarProps {
  currentTool: AnnotationTool
  onToolChange: (tool: AnnotationTool) => void
  currentLabel: string
  onLabelChange: (label: string) => void
  predefinedLabels: Array<{ name: string; color: string }>
  zoom: number
  onZoomChange: (zoom: number) => void
  showLabels: boolean
  onShowLabelsToggle: () => void
  canUndo: boolean
  canRedo: boolean
  onUndo: () => void
  onRedo: () => void
  onSave: () => void
  onClear: () => void
}

// Simple Tooltip component
interface TooltipProps {
  content: string
  children: React.ReactNode
}

function Tooltip({ content, children }: TooltipProps) {
  return (
    <div className="relative group">
      {children}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
        {content}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
      </div>
    </div>
  )
}

export function CompactAnnotationToolbar({
  currentTool,
  onToolChange,
  currentLabel,
  onLabelChange,
  predefinedLabels,
  zoom,
  onZoomChange,
  showLabels,
  onShowLabelsToggle,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onClear,
}: CompactAnnotationToolbarProps) {
  const [showLabelPanel, setShowLabelPanel] = useState(false)

  const tools = [
    { id: 'select' as const, icon: MousePointer, label: '选择工具' },
    { id: 'rectangle' as const, icon: Square, label: '矩形标注' },
    { id: 'circle' as const, icon: Circle, label: '圆形标注' },
    { id: 'point' as const, icon: MapPin, label: '点标注' },
  ]

  const getCurrentColor = () => {
    const labelConfig = predefinedLabels.find(l => l.name === currentLabel)
    return labelConfig?.color || '#ef4444'
  }

  return (
    <div className="absolute top-4 left-4 z-10">
      <div className="bg-white rounded-lg shadow-lg border p-2">
        <div className="flex items-center space-x-1">
          {/* Annotation Tools */}
          <div className="flex items-center space-x-1 border-r pr-2">
            {tools.map((tool) => {
              const Icon = tool.icon
              return (
                <Tooltip key={tool.id} content={tool.label}>
                  <Button
                    variant={currentTool === tool.id ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => onToolChange(tool.id)}
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                </Tooltip>
              )
            })}
          </div>

          {/* Label and Color */}
          <div className="flex items-center space-x-1 border-r pr-2">
            <Tooltip content="标签管理">
              <Button
                variant={showLabelPanel ? 'default' : 'ghost'}
                size="icon"
                className="h-8 w-8"
                onClick={() => setShowLabelPanel(!showLabelPanel)}
              >
                <Tag className="h-4 w-4" />
              </Button>
            </Tooltip>
            
            {currentLabel && (
              <Tooltip content={`当前标签: ${currentLabel}`}>
                <div 
                  className="w-6 h-6 rounded border-2 border-gray-300 cursor-pointer"
                  style={{ backgroundColor: getCurrentColor() }}
                  onClick={() => setShowLabelPanel(!showLabelPanel)}
                />
              </Tooltip>
            )}
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-1 border-r pr-2">
            <Tooltip content={showLabels ? '隐藏标签' : '显示标签'}>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onShowLabelsToggle}
              >
                {showLabels ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              </Button>
            </Tooltip>

            <Tooltip content="放大">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onZoomChange(Math.min(zoom + 0.1, 3))}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </Tooltip>

            <Tooltip content="缩小">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onZoomChange(Math.max(zoom - 0.1, 0.1))}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
            </Tooltip>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-1">
            <Tooltip content="撤销">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onUndo}
                disabled={!canUndo}
              >
                <Undo className="h-4 w-4" />
              </Button>
            </Tooltip>

            <Tooltip content="重做">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onRedo}
                disabled={!canRedo}
              >
                <Redo className="h-4 w-4" />
              </Button>
            </Tooltip>

            <Tooltip content="保存标注">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onSave}
              >
                <Save className="h-4 w-4" />
              </Button>
            </Tooltip>

            <Tooltip content="清空所有">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onClear}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </Tooltip>
          </div>
        </div>

        {/* Label Panel */}
        {showLabelPanel && (
          <div className="mt-2 pt-2 border-t">
            <div className="space-y-2 w-64">
              {/* Predefined Labels */}
              <div>
                <div className="text-xs font-medium text-gray-700 mb-1">预定义标签</div>
                <div className="flex flex-wrap gap-1">
                  {predefinedLabels.map((label) => (
                    <button
                      key={label.name}
                      className={`px-2 py-1 text-xs rounded border ${
                        currentLabel === label.name 
                          ? 'border-gray-800 bg-gray-100' 
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      style={{ 
                        borderLeftColor: label.color,
                        borderLeftWidth: '3px'
                      }}
                      onClick={() => onLabelChange(label.name)}
                    >
                      {label.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Custom Label Input */}
              <div>
                <div className="text-xs font-medium text-gray-700 mb-1">自定义标签</div>
                <Input
                  placeholder="输入标签名称"
                  value={currentLabel}
                  onChange={(e) => onLabelChange(e.target.value)}
                  className="h-8 text-xs"
                />
              </div>

              {/* Zoom Control */}
              <div>
                <div className="text-xs font-medium text-gray-700 mb-1">
                  缩放: {Math.round(zoom * 100)}%
                </div>
                <input
                  type="range"
                  min="0.1"
                  max="3"
                  step="0.1"
                  value={zoom}
                  onChange={(e) => onZoomChange(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

# 🎯 图像标注工作流实现

## 📋 功能概述

实现了完整的图像标注工作流程，用户可以：

1. **查看数据集列表** - 在annotations页面查看可用于标注的数据集
2. **选择数据集** - 点击数据集详情查看数据集信息
3. **开始标注** - 点击"开始标记"按钮进入图像标注页面
4. **图片导航** - 在标注页面中浏览数据集中的所有图片
5. **上一张/下一张** - 使用导航按钮切换图片

## 🔄 完整工作流程

### 1. 标注管理页面 (`/annotations`)
- **功能**: 显示可用于标注的数据集列表
- **筛选条件**: 只显示类型为"IMAGE"且状态为"READY"的数据集
- **操作**: 
  - 查看数据集详情
  - 直接开始标记

### 2. 数据集详情页面 (`/datasets/[id]`)
- **新增功能**: "开始标记"按钮
- **显示条件**: 只有图像数据集且状态为就绪时才显示
- **跳转**: 点击后跳转到图像标注页面并传递数据集ID

### 3. 图像标注页面 (`/annotations/image-annotate`)
- **参数支持**: 接收`datasetId`查询参数
- **图片加载**: 自动加载指定数据集中的所有图片
- **导航功能**: 
  - 显示当前图片位置 (x / total)
  - 上一张/下一张按钮
  - 按钮状态管理（首张/末张时禁用）

## 🛠 技术实现

### 修改的文件

1. **`app/annotations/page.tsx`**
   - 从显示标注列表改为显示数据集列表
   - 添加数据集筛选逻辑
   - 更新UI组件和样式

2. **`app/datasets/[id]/page.tsx`**
   - 添加"开始标记"按钮
   - 条件显示（仅图像数据集且状态就绪）
   - 使用Link组件跳转

3. **`app/annotations/image-annotate/page.tsx`**
   - 添加图片列表状态管理
   - 实现数据集图片加载功能
   - 添加图片导航功能
   - 更新UI显示图片位置信息

4. **`constants/routes.ts`**
   - 添加图像标注相关路由常量
   - 支持数据集参数的路由

5. **`services/mockData.ts`**
   - 增加更多测试图片数据
   - 确保每个数据集都有多张图片用于测试导航

### 核心功能

#### 数据集图片加载
```typescript
const loadDatasetImages = async (datasetId: string) => {
  const images = await imageService.getImagesByDataset(datasetId)
  if (images.length > 0) {
    setImageList(images)
    setCurrentImageIndex(0)
    setSelectedImage(images[0])
  }
}
```

#### 图片导航
```typescript
const goToPreviousImage = () => {
  if (currentImageIndex > 0) {
    const newIndex = currentImageIndex - 1
    setCurrentImageIndex(newIndex)
    setSelectedImage(imageList[newIndex])
    // 切换图片时清空标注
    setAnnotations([])
  }
}
```

## 📊 数据结构

### 数据集筛选条件
- `type === 'IMAGE'` - 只显示图像数据集
- `status === 'READY'` - 只显示就绪状态的数据集

### 图片数据关联
- 每个图片都有`datasetId`字段关联到对应数据集
- 通过`imageService.getImagesByDataset(datasetId)`获取数据集图片

## 🎨 用户界面

### 标注管理页面
- 网格布局显示数据集卡片
- 每个卡片显示数据集基本信息
- "开始标记"按钮直接跳转到标注页面

### 图像标注页面
- Header显示当前图片信息和位置
- 左右导航按钮（ChevronLeft/ChevronRight）
- 按钮状态管理（禁用状态）
- 图片计数显示 "(1 / 5)"

## 🔧 路由配置

```typescript
// 新增路由常量
IMAGE_ANNOTATE: '/annotations/image-annotate',
IMAGE_ANNOTATE_DATASET: (datasetId: string) => 
  `/annotations/image-annotate?datasetId=${datasetId}`,
```

## ✅ 测试数据

为了测试功能，添加了以下测试数据：
- 数据集1：5张图片（猫、狗、鸟等动物图片）
- 数据集2：3张图片（街景、交通相关图片）
- 所有图片都使用Unsplash高质量图片

## 🚀 使用方法

1. 访问 `/annotations` 查看可标注的数据集
2. 点击任意数据集的"开始标记"按钮
3. 在标注页面使用左右箭头按钮切换图片
4. 每张图片都可以独立进行标注
5. 切换图片时会清空当前标注（避免混淆）

## 📝 注意事项

- 切换图片时会清空当前标注，确保标注数据的准确性
- 只有图像类型且状态为就绪的数据集才能进行标注
- 导航按钮在首张/末张图片时会自动禁用
- 支持键盘快捷键（可以后续扩展）

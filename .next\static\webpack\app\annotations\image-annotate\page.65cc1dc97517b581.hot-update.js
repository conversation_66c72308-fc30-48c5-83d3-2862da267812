"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, predefinedLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect, onAnnotationDelete } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        x: 0,\n        y: 0,\n        targetAnnotation: null,\n        clickPosition: null\n    });\n    const [globalMousePosition, setGlobalMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    // Global mouse tracking for context menu following\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            let animationFrame;\n            const handleGlobalMouseMove = {\n                \"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\": (e)=>{\n                    // Use requestAnimationFrame for smooth updates\n                    if (animationFrame) {\n                        cancelAnimationFrame(animationFrame);\n                    }\n                    animationFrame = requestAnimationFrame({\n                        \"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\": ()=>{\n                            setGlobalMousePosition({\n                                x: e.clientX,\n                                y: e.clientY\n                            });\n                        }\n                    }[\"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\"]);\n                }\n            }[\"ImageAnnotationCanvas.useEffect.handleGlobalMouseMove\"];\n            if (contextMenu.show) {\n                document.addEventListener('mousemove', handleGlobalMouseMove, {\n                    passive: true\n                });\n                return ({\n                    \"ImageAnnotationCanvas.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleGlobalMouseMove);\n                        if (animationFrame) {\n                            cancelAnimationFrame(animationFrame);\n                        }\n                    }\n                })[\"ImageAnnotationCanvas.useEffect\"];\n            }\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        contextMenu.show\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    const handleContextMenu = (e)=>{\n        var _canvasRef_current;\n        e.preventDefault();\n        const coords = getCanvasCoordinates(e);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // Check if right-clicking on an existing annotation\n        const clickedAnnotation = annotations.find((annotation)=>{\n            switch(annotation.type){\n                case 'rectangle':\n                    const [x, y, width, height] = annotation.coordinates;\n                    return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                case 'circle':\n                    const [cx, cy, radius] = annotation.coordinates;\n                    const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                    return distance <= radius;\n                case 'point':\n                    const [px, py] = annotation.coordinates;\n                    const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                    return pointDistance <= 10;\n                default:\n                    return false;\n            }\n        });\n        setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            targetAnnotation: clickedAnnotation || null,\n            clickPosition: coords\n        });\n    };\n    const hideContextMenu = ()=>{\n        setContextMenu((prev)=>({\n                ...prev,\n                show: false\n            }));\n    };\n    // Calculate menu position with boundary detection\n    const getMenuPosition = ()=>{\n        const menuWidth = 192 // min-w-48 = 192px\n        ;\n        const menuHeight = 200 // estimated menu height\n        ;\n        const offset = 10;\n        let x = globalMousePosition.x + offset;\n        let y = globalMousePosition.y + offset;\n        // Check right boundary\n        if (x + menuWidth > window.innerWidth) {\n            x = globalMousePosition.x - menuWidth - offset;\n        }\n        // Check bottom boundary\n        if (y + menuHeight > window.innerHeight) {\n            y = globalMousePosition.y - menuHeight - offset;\n        }\n        // Ensure menu doesn't go off-screen\n        x = Math.max(0, Math.min(x, window.innerWidth - menuWidth));\n        y = Math.max(0, Math.min(y, window.innerHeight - menuHeight));\n        return {\n            x,\n            y\n        };\n    };\n    // Context menu actions\n    const handleDeleteAnnotation = ()=>{\n        if (contextMenu.targetAnnotation) {\n            onAnnotationDelete(contextMenu.targetAnnotation.id);\n        }\n        hideContextMenu();\n    };\n    const handleEditLabel = ()=>{\n        if (contextMenu.targetAnnotation) {\n            setEditingLabel(contextMenu.targetAnnotation.id);\n            setEditingLabelValue(contextMenu.targetAnnotation.label);\n        }\n        hideContextMenu();\n    };\n    const handleAddAnnotation = (type)=>{\n        if (!contextMenu.clickPosition) return;\n        const coords = contextMenu.clickPosition;\n        let newAnnotation;\n        if (type === 'point') {\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'rectangle') {\n            // Create a small default rectangle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x - 25,\n                    coords.y - 25,\n                    50,\n                    50\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else if (type === 'circle') {\n            // Create a small default circle\n            newAnnotation = {\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    25\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n        } else {\n            return;\n        }\n        onAnnotationCreate(newAnnotation);\n        hideContextMenu();\n    };\n    const handleChangeLabel = (labelName)=>{\n        if (contextMenu.targetAnnotation) {\n            const labelConfig = predefinedLabels.find((l)=>l.name === labelName);\n            onAnnotationUpdate(contextMenu.targetAnnotation.id, {\n                label: labelName,\n                color: (labelConfig === null || labelConfig === void 0 ? void 0 : labelConfig.color) || currentColor\n            });\n        }\n        hideContextMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 531,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 539,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick,\n                onContextMenu: handleContextMenu,\n                onClick: hideContextMenu\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 574,\n                columnNumber: 9\n            }, this),\n            contextMenu.show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: hideContextMenu\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed z-50 bg-white rounded-lg shadow-lg border py-2 min-w-48 pointer-events-auto transition-all duration-75\",\n                        style: {\n                            left: getMenuPosition().x,\n                            top: getMenuPosition().y\n                        },\n                        children: contextMenu.targetAnnotation ? // Menu for existing annotation\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: [\n                                        contextMenu.targetAnnotation.label,\n                                        \" (\",\n                                        contextMenu.targetAnnotation.type,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: handleEditLabel,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"编辑标签\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1 text-xs text-gray-500\",\n                                    children: \"更改标签为:\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 17\n                                }, this),\n                                predefinedLabels.map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full px-3 py-1 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                        onClick: ()=>handleChangeLabel(label.name),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded mr-2 border\",\n                                                style: {\n                                                    backgroundColor: label.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 21\n                                            }, this),\n                                            label.name\n                                        ]\n                                    }, label.name, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t my-1\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center\",\n                                    onClick: handleDeleteAnnotation,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDDD1️\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"删除标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : // Menu for empty area\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-700 border-b\",\n                                    children: \"添加标注\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('point'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"\\uD83D\\uDCCD\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加点标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('rectangle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⬜\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加矩形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center\",\n                                    onClick: ()=>handleAddAnnotation('circle'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"⭕\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"添加圆形标注\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 526,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"IDHObakyPsHr+JpRBpdLTwKuRL0=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
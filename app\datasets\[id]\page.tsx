'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ROUTES } from '@/constants/routes'
import { ArrowLeft, Edit, Database, Calendar, FileText, Download, Upload, Image } from 'lucide-react'
import { mockDatasets, getProjectById } from '@/services/mockData'

interface DatasetDetailProps {
  params: Promise<{
    id: string
  }>
}

export default function DatasetDetailPage({ params }: DatasetDetailProps) {
  const [dataset, setDataset] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    // Await params and load dataset data
    params.then(({ id }) => {
      const datasetData = mockDatasets.find(d => d.id === id)
      setDataset(datasetData)
    })
  }, [params])

  if (!dataset) {
    return <div className="flex items-center justify-center min-h-screen">加载中...</div>
  }

  const project = getProjectById(dataset.projectId)

  const statusColors = {
    UPLOADING: 'bg-blue-100 text-blue-800',
    PROCESSING: 'bg-yellow-100 text-yellow-800',
    READY: 'bg-green-100 text-green-800',
    ERROR: 'bg-red-100 text-red-800',
  }

  const statusLabels = {
    UPLOADING: '上传中',
    PROCESSING: '处理中',
    READY: '就绪',
    ERROR: '错误',
  }

  const typeLabels = {
    IMAGE: '图像',
    VIDEO: '视频',
    AUDIO: '音频',
    TEXT: '文本',
    MULTIMODAL: '多模态',
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{dataset.name}</h1>
            <p className="text-gray-600 mt-2">{dataset.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {dataset.type === 'IMAGE' && dataset.status === 'READY' && (
            <Link href={ROUTES.IMAGE_ANNOTATE_DATASET(dataset.id)}>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Image className="h-4 w-4 mr-2" />
                开始标记
              </Button>
            </Link>
          )}
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            下载
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            上传更多
          </Button>
          <Button size="sm">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据状态</CardTitle>
          </CardHeader>
          <CardContent>
            <span
              className={`px-2 py-1 text-xs rounded-full ${
                statusColors[dataset.status as keyof typeof statusColors]
              }`}
            >
              {statusLabels[dataset.status as keyof typeof statusLabels]}
            </span>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据类型</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {typeLabels[dataset.type as keyof typeof typeLabels]}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">文件数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dataset.size.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">存储大小</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(dataset.size * 0.5).toFixed(1)} GB
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Dataset Info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <span className="text-sm font-medium text-gray-700">数据集名称</span>
                <div className="mt-1 text-sm text-gray-900">{dataset.name}</div>
              </div>

              <div>
                <span className="text-sm font-medium text-gray-700">描述</span>
                <div className="mt-1 text-sm text-gray-900">{dataset.description}</div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-500">
                  <Database className="h-4 w-4 mr-2" />
                  数据类型：{typeLabels[dataset.type as keyof typeof typeLabels]}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <FileText className="h-4 w-4 mr-2" />
                  关联项目：{project?.name || '未关联项目'}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  创建时间：{dataset.createdAt.toLocaleDateString()}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  更新时间：{dataset.updatedAt.toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>数据统计</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">总文件数</span>
                <span className="text-sm font-medium">{dataset.size.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">预估大小</span>
                <span className="text-sm font-medium">{(dataset.size * 0.5).toFixed(1)} GB</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">平均文件大小</span>
                <span className="text-sm font-medium">
                  {((dataset.size * 0.5 * 1024) / dataset.size).toFixed(1)} MB
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Preview */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>数据预览</CardTitle>
              <CardDescription>
                数据集中的示例文件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <Database className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-xs">示例文件 {i}</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-gray-50 rounded-md">
                <h4 className="text-sm font-medium text-gray-700 mb-2">文件格式分布：</h4>
                <div className="space-y-1 text-xs text-gray-600">
                  {dataset.type === 'IMAGE' && (
                    <>
                      <div className="flex justify-between">
                        <span>JPG</span>
                        <span>45%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>PNG</span>
                        <span>35%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>其他</span>
                        <span>20%</span>
                      </div>
                    </>
                  )}
                  {dataset.type === 'AUDIO' && (
                    <>
                      <div className="flex justify-between">
                        <span>WAV</span>
                        <span>60%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>MP3</span>
                        <span>40%</span>
                      </div>
                    </>
                  )}
                  {dataset.type === 'TEXT' && (
                    <>
                      <div className="flex justify-between">
                        <span>TXT</span>
                        <span>70%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>JSON</span>
                        <span>30%</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        const coords = getCanvasCoordinates(e);\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseUp\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"cKR0bKpXOT41g3nXy0GIr86jYvE=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});
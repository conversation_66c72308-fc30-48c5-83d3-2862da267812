"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/annotations/image-annotate/page",{

/***/ "(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx":
/*!***********************************************************!*\
  !*** ./components/annotation/image-annotation-canvas.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageAnnotationCanvas: () => (/* binding */ ImageAnnotationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ImageAnnotationCanvas auto */ \nvar _s = $RefreshSig$();\n\nfunction ImageAnnotationCanvas(param) {\n    let { imageUrl, imageWidth, imageHeight, annotations, selectedAnnotation, currentTool, currentLabel, currentColor, zoom, showLabels, onAnnotationCreate, onAnnotationUpdate, onAnnotationSelect } = param;\n    var _annotations_find, _annotations_find1;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDrawing, setIsDrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startPoint, setStartPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAnnotation, setCurrentAnnotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabel, setEditingLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingLabelValue, setEditingLabelValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            console.log('ImageAnnotationCanvas props:', {\n                imageUrl,\n                imageWidth,\n                imageHeight,\n                imageLoaded\n            });\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        imageUrl,\n        imageWidth,\n        imageHeight,\n        imageLoaded\n    ]);\n    const drawAnnotations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (ctx)=>{\n            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n            // Draw image\n            const image = imageRef.current;\n            if (image && image.complete) {\n                ctx.drawImage(image, 0, 0, imageWidth, imageHeight);\n            }\n            // Draw annotations\n            annotations.forEach({\n                \"ImageAnnotationCanvas.useCallback[drawAnnotations]\": (annotation)=>{\n                    ctx.strokeStyle = annotation.color;\n                    ctx.fillStyle = annotation.color + '30' // 30% opacity\n                    ;\n                    ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2;\n                    switch(annotation.type){\n                        case 'rectangle':\n                            const [x, y, width, height] = annotation.coordinates;\n                            ctx.strokeRect(x, y, width, height);\n                            ctx.fillRect(x, y, width, height);\n                            break;\n                        case 'circle':\n                            const [cx, cy, radius] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ctx.stroke();\n                            ctx.fill();\n                            break;\n                        case 'point':\n                            const [px, py] = annotation.coordinates;\n                            ctx.beginPath();\n                            ctx.arc(px, py, 5, 0, 2 * Math.PI);\n                            ctx.fill();\n                            break;\n                    }\n                    // Draw label\n                    if (showLabels && annotation.label) {\n                        ctx.fillStyle = annotation.color;\n                        ctx.font = 'bold 14px Arial';\n                        ctx.strokeStyle = 'white';\n                        ctx.lineWidth = 3;\n                        const labelX = annotation.coordinates[0];\n                        const labelY = annotation.coordinates[1] - 8;\n                        // Draw text outline\n                        ctx.strokeText(annotation.label, labelX, labelY);\n                        // Draw text\n                        ctx.fillText(annotation.label, labelX, labelY);\n                        // Draw edit indicator for selected annotation\n                        if (annotation.id === selectedAnnotation) {\n                            ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';\n                            ctx.strokeStyle = '#3b82f6';\n                            ctx.lineWidth = 1;\n                            const textWidth = ctx.measureText(annotation.label).width;\n                            ctx.fillRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                            ctx.strokeRect(labelX - 2, labelY - 16, textWidth + 4, 20);\n                        }\n                    }\n                }\n            }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"]);\n            // Draw current annotation being created\n            if (currentAnnotation) {\n                ctx.strokeStyle = currentColor;\n                ctx.fillStyle = currentColor + '30';\n                ctx.lineWidth = 2;\n                ctx.setLineDash([\n                    5,\n                    5\n                ]);\n                switch(currentAnnotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = currentAnnotation.coordinates;\n                        ctx.strokeRect(x, y, width, height);\n                        break;\n                    case 'circle':\n                        const [cx, cy, radius] = currentAnnotation.coordinates;\n                        ctx.beginPath();\n                        ctx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        break;\n                }\n                ctx.setLineDash([]);\n            }\n            // Draw mouse coordinates\n            if (mousePosition) {\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.font = '12px Arial';\n                const coordText = \"(\".concat(Math.round(mousePosition.x), \", \").concat(Math.round(mousePosition.y), \")\");\n                const textWidth = ctx.measureText(coordText).width;\n                // Position the coordinate display near the mouse but within canvas bounds\n                let displayX = mousePosition.x + 10;\n                let displayY = mousePosition.y - 10;\n                // Adjust position if it would go outside canvas\n                if (displayX + textWidth + 10 > imageWidth) {\n                    displayX = mousePosition.x - textWidth - 10;\n                }\n                if (displayY < 20) {\n                    displayY = mousePosition.y + 25;\n                }\n                // Draw background\n                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n                ctx.fillRect(displayX - 5, displayY - 15, textWidth + 10, 20);\n                // Draw text\n                ctx.fillStyle = 'white';\n                ctx.fillText(coordText, displayX, displayY);\n            }\n        }\n    }[\"ImageAnnotationCanvas.useCallback[drawAnnotations]\"], [\n        annotations,\n        selectedAnnotation,\n        showLabels,\n        currentAnnotation,\n        imageWidth,\n        imageHeight,\n        currentColor,\n        mousePosition\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageAnnotationCanvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = imageWidth;\n            canvas.height = imageHeight;\n            drawAnnotations(ctx);\n        }\n    }[\"ImageAnnotationCanvas.useEffect\"], [\n        drawAnnotations\n    ]);\n    const getCanvasCoordinates = (e)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return {\n            x: 0,\n            y: 0\n        };\n        const rect = canvas.getBoundingClientRect();\n        return {\n            x: (e.clientX - rect.left) / zoom,\n            y: (e.clientY - rect.top) / zoom\n        };\n    };\n    const handleMouseDown = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        if (currentTool === 'select') {\n            // Check if clicking on an existing annotation\n            const clickedAnnotation = annotations.find((annotation)=>{\n                switch(annotation.type){\n                    case 'rectangle':\n                        const [x, y, width, height] = annotation.coordinates;\n                        return coords.x >= x && coords.x <= x + width && coords.y >= y && coords.y <= y + height;\n                    case 'circle':\n                        const [cx, cy, radius] = annotation.coordinates;\n                        const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2);\n                        return distance <= radius;\n                    case 'point':\n                        const [px, py] = annotation.coordinates;\n                        const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2);\n                        return pointDistance <= 10;\n                    default:\n                        return false;\n                }\n            });\n            onAnnotationSelect((clickedAnnotation === null || clickedAnnotation === void 0 ? void 0 : clickedAnnotation.id) || null);\n            return;\n        }\n        if (currentTool === 'point') {\n            const newAnnotation = {\n                id: Date.now().toString(),\n                type: 'point',\n                coordinates: [\n                    coords.x,\n                    coords.y\n                ],\n                label: currentLabel,\n                color: currentColor\n            };\n            onAnnotationCreate(newAnnotation);\n            return;\n        }\n        setIsDrawing(true);\n        setStartPoint(coords);\n        if (currentTool === 'rectangle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'rectangle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        } else if (currentTool === 'circle') {\n            setCurrentAnnotation({\n                id: Date.now().toString(),\n                type: 'circle',\n                coordinates: [\n                    coords.x,\n                    coords.y,\n                    0\n                ],\n                label: currentLabel,\n                color: currentColor\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Always update mouse position for coordinate display\n        setMousePosition(coords);\n        if (!isDrawing || !startPoint || !currentAnnotation) return;\n        if (currentAnnotation.type === 'rectangle') {\n            const width = coords.x - startPoint.x;\n            const height = coords.y - startPoint.y;\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    width,\n                    height\n                ]\n            });\n        } else if (currentAnnotation.type === 'circle') {\n            const radius = Math.sqrt((coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2);\n            setCurrentAnnotation({\n                ...currentAnnotation,\n                coordinates: [\n                    startPoint.x,\n                    startPoint.y,\n                    radius\n                ]\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        if (isDrawing && currentAnnotation) {\n            // Only create annotation if it has meaningful size\n            const isValidAnnotation = currentAnnotation.type === 'rectangle' && Math.abs(currentAnnotation.coordinates[2]) > 5 && Math.abs(currentAnnotation.coordinates[3]) > 5 || currentAnnotation.type === 'circle' && currentAnnotation.coordinates[2] > 5;\n            if (isValidAnnotation) {\n                onAnnotationCreate(currentAnnotation);\n            }\n        }\n        setIsDrawing(false);\n        setStartPoint(null);\n        setCurrentAnnotation(null);\n    };\n    const handleMouseLeave = ()=>{\n        setMousePosition(null);\n        handleMouseUp();\n    };\n    const handleDoubleClick = (e)=>{\n        const coords = getCanvasCoordinates(e);\n        // Check if double-clicking on a label to edit it\n        const clickedAnnotation = annotations.find((annotation)=>{\n            const labelX = annotation.coordinates[0];\n            const labelY = annotation.coordinates[1] - 8;\n            const canvas = canvasRef.current;\n            if (!canvas) return false;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return false;\n            ctx.font = 'bold 14px Arial';\n            const textWidth = ctx.measureText(annotation.label).width;\n            return coords.x >= labelX - 2 && coords.x <= labelX + textWidth + 2 && coords.y >= labelY - 16 && coords.y <= labelY + 4;\n        });\n        if (clickedAnnotation) {\n            setEditingLabel(clickedAnnotation.id);\n            setEditingLabelValue(clickedAnnotation.label);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-gray-300 rounded-lg overflow-hidden bg-white\",\n        style: {\n            width: imageWidth,\n            height: imageHeight\n        },\n        children: [\n            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"加载图像中...\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                ref: imageRef,\n                src: imageUrl,\n                alt: \"Annotation target\",\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onLoad: ()=>{\n                    console.log('Image loaded successfully:', imageUrl);\n                    setImageLoaded(true);\n                    const canvas = canvasRef.current;\n                    if (canvas) {\n                        const ctx = canvas.getContext('2d');\n                        if (ctx) drawAnnotations(ctx);\n                    }\n                },\n                onError: (e)=>{\n                    console.error('Image failed to load:', imageUrl, e);\n                    setImageLoaded(false);\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute top-0 left-0 cursor-crosshair\",\n                style: {\n                    width: imageWidth,\n                    height: imageHeight\n                },\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseLeave,\n                onDoubleClick: handleDoubleClick\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, this),\n            editingLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: editingLabelValue,\n                    onChange: (e)=>setEditingLabelValue(e.target.value),\n                    onBlur: ()=>{\n                        if (editingLabelValue.trim()) {\n                            onAnnotationUpdate(editingLabel, {\n                                label: editingLabelValue.trim()\n                            });\n                        }\n                        setEditingLabel(null);\n                        setEditingLabelValue('');\n                    },\n                    onKeyDown: (e)=>{\n                        if (e.key === 'Enter') {\n                            if (editingLabelValue.trim()) {\n                                onAnnotationUpdate(editingLabel, {\n                                    label: editingLabelValue.trim()\n                                });\n                            }\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        } else if (e.key === 'Escape') {\n                            setEditingLabel(null);\n                            setEditingLabelValue('');\n                        }\n                    },\n                    className: \"pointer-events-auto px-2 py-1 text-sm border border-blue-500 rounded bg-white shadow-lg\",\n                    style: {\n                        position: 'absolute',\n                        left: ((_annotations_find = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find === void 0 ? void 0 : _annotations_find.coordinates[0]) || 0,\n                        top: (((_annotations_find1 = annotations.find((a)=>a.id === editingLabel)) === null || _annotations_find1 === void 0 ? void 0 : _annotations_find1.coordinates[1]) || 0) - 30,\n                        zIndex: 1000\n                    },\n                    autoFocus: true\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\annotation\\\\image-annotation-canvas.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageAnnotationCanvas, \"YTGJpgkBX9L8LqWTy4YRdKpxjIg=\");\n_c = ImageAnnotationCanvas;\nvar _c;\n$RefreshReg$(_c, \"ImageAnnotationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/annotation/image-annotation-canvas.tsx\n"));

/***/ })

});